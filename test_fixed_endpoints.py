"""
Test the fixed endpoints to verify 100% functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_stock_analysis_endpoint():
    """Test the fixed stock analysis endpoint"""
    print("🔧 Testing Fixed Stock Analysis Endpoint")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/predicto/analyze",
            json={"symbol": "AAPL"},
            timeout=15
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Stock Analysis: SUCCESS")
            print(f"   Symbol: {data.get('symbol', 'unknown')}")
            print(f"   Powered by: {data.get('powered_by', 'unknown')}")
            print(f"   Has analysis: {'analysis' in data}")
            return True
        else:
            print(f"❌ Stock Analysis: HTTP {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Stock Analysis: {e}")
        return False

def test_all_core_endpoints():
    """Test all core endpoints"""
    print("🔮 Testing All Core Endpoints")
    print("=" * 40)
    
    tests = [
        ("Health Check", lambda: requests.get(f"{BASE_URL}/api/v1/health", timeout=5)),
        ("Predicto Capabilities", lambda: requests.get(f"{BASE_URL}/api/v1/predicto/capabilities", timeout=10)),
        ("Predicto Chat", lambda: requests.post(f"{BASE_URL}/api/v1/chat", json={"message": "Test", "session_id": "test"}, timeout=10)),
        ("Web Interface", lambda: requests.get(f"{BASE_URL}/", timeout=10))
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            response = test_func()
            if response.status_code == 200:
                print(f"✅ {test_name}: Working")
                passed += 1
            else:
                print(f"❌ {test_name}: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {test_name}: {e}")
    
    # Test the fixed stock analysis endpoint
    if test_stock_analysis_endpoint():
        passed += 1
    
    total = len(tests) + 1  # +1 for stock analysis
    success_rate = (passed / total) * 100
    
    print(f"\n{'='*40}")
    print(f"Results: {passed}/{total} endpoints working ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("🎉 100% SUCCESS! All endpoints working perfectly!")
        return True
    else:
        print(f"⚠️ {total - passed} endpoints still have issues")
        return False

def test_error_handling():
    """Test error handling"""
    print("\n🛡️ Testing Error Handling")
    
    tests_passed = 0
    total_tests = 0
    
    # Test invalid JSON
    total_tests += 1
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            data="invalid json",
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        if response.status_code == 422:
            print("✅ Invalid JSON: Proper error handling")
            tests_passed += 1
        else:
            print(f"❌ Invalid JSON: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Invalid JSON: {e}")
    
    # Test missing fields
    total_tests += 1
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"session_id": "test"},  # Missing message
            timeout=5
        )
        if response.status_code == 422:
            print("✅ Missing fields: Proper validation")
            tests_passed += 1
        else:
            print(f"❌ Missing fields: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Missing fields: {e}")
    
    # Test invalid endpoint
    total_tests += 1
    try:
        response = requests.get(f"{BASE_URL}/api/v1/nonexistent", timeout=5)
        if response.status_code == 404:
            print("✅ Invalid endpoint: Proper 404")
            tests_passed += 1
        else:
            print(f"❌ Invalid endpoint: Status {response.status_code}")
    except Exception as e:
        print(f"❌ Invalid endpoint: {e}")
    
    error_success_rate = (tests_passed / total_tests) * 100
    print(f"Error Handling: {tests_passed}/{total_tests} tests passed ({error_success_rate:.1f}%)")
    
    return error_success_rate == 100

def main():
    """Run comprehensive tests"""
    print("🔮 COMPREHENSIVE PREDICTO FUNCTIONALITY TEST")
    print("=" * 60)
    print("Testing all endpoints and error handling...")
    print("=" * 60)
    
    # Test all endpoints
    endpoints_working = test_all_core_endpoints()
    
    # Test error handling
    error_handling_working = test_error_handling()
    
    print("\n" + "=" * 60)
    print("🔮 FINAL RESULTS")
    print("=" * 60)
    
    if endpoints_working and error_handling_working:
        print("🎉 100% SUCCESS! PRODUCTION READY!")
        print("✅ All endpoints working perfectly")
        print("✅ Error handling working correctly")
        print("✅ Predicto integration is complete and functional")
        print("\n🚀 System is ready for production use!")
        return True
    else:
        print("⚠️ Some issues remain:")
        if not endpoints_working:
            print("   - Some endpoints not working")
        if not error_handling_working:
            print("   - Error handling needs attention")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
