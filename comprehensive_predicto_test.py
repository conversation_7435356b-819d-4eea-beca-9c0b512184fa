"""
Comprehensive Predicto Chatbot Testing
Tests multiple conversation scenarios and evaluates response quality
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8080"

class PredictoChatTester:
    def __init__(self):
        self.test_results = []
        self.session_id = f"test_session_{int(time.time())}"
    
    def test_conversation(self, test_name, message, expected_criteria=None):
        """Test a single conversation and evaluate the response"""
        print(f"\n{'='*60}")
        print(f"🔮 TEST: {test_name}")
        print(f"{'='*60}")
        print(f"Message: '{message}'")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{BASE_URL}/api/v1/chat",
                json={"message": message, "session_id": self.session_id},
                timeout=20
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                context = data.get('context', {})
                
                print(f"✅ Status: {response.status_code} (Response time: {response_time:.2f}s)")
                print(f"Response Type: {data.get('type', 'unknown')}")
                print(f"Confidence: {data.get('confidence', 'unknown')}")
                
                # Check branding
                powered_by = context.get('powered_by', 'Not found')
                system = context.get('system', 'Not found')
                print(f"Powered By: {powered_by}")
                print(f"System: {system}")
                
                # Evaluate response quality
                evaluation = self.evaluate_response(response_text, context, expected_criteria)
                
                print(f"\n📊 RESPONSE EVALUATION:")
                for criterion, passed in evaluation.items():
                    status = "✅" if passed else "❌"
                    print(f"   {status} {criterion}")
                
                print(f"\n💬 RESPONSE PREVIEW:")
                print(f"   {response_text[:200]}...")
                if len(response_text) > 200:
                    print(f"   [Response continues for {len(response_text)} total characters]")
                
                # Store results
                test_result = {
                    'test_name': test_name,
                    'message': message,
                    'status_code': response.status_code,
                    'response_time': response_time,
                    'response_text': response_text,
                    'context': context,
                    'evaluation': evaluation,
                    'overall_pass': all(evaluation.values())
                }
                self.test_results.append(test_result)
                
                return test_result
                
            else:
                print(f"❌ Status: {response.status_code}")
                print(f"Error: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return None
    
    def evaluate_response(self, response_text, context, expected_criteria):
        """Evaluate response quality against Predicto standards"""
        evaluation = {}
        
        # Check branding
        powered_by = context.get('powered_by', '')
        evaluation['Correct Branding'] = 'A.T.L.A.S powered by Predicto' in powered_by
        
        # Check for false initialization messages
        evaluation['No False Init Message'] = 'AI systems are currently initializing' not in response_text
        
        # Check response length (should be substantial)
        evaluation['Substantial Response'] = len(response_text) > 50
        
        # Check for Predicto identity
        evaluation['Predicto Identity'] = 'Predicto' in response_text or 'predicto' in response_text.lower()
        
        # Check for A.T.L.A.S mention
        evaluation['A.T.L.A.S Reference'] = 'A.T.L.A.S' in response_text or 'atlas' in response_text.lower()
        
        # Check for professional tone (no error messages in response)
        evaluation['Professional Tone'] = not any(word in response_text.lower() for word in ['error', 'failed', 'exception', 'traceback'])
        
        # Apply specific criteria if provided
        if expected_criteria:
            for criterion, check_func in expected_criteria.items():
                evaluation[criterion] = check_func(response_text, context)
        
        return evaluation
    
    def run_comprehensive_tests(self):
        """Run all test scenarios"""
        print("🔮 COMPREHENSIVE PREDICTO CHATBOT TESTING")
        print("=" * 80)
        print(f"Test Session ID: {self.session_id}")
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # Test 1: Greeting Messages
        greeting_criteria = {
            'Welcome Message': lambda text, ctx: any(word in text.lower() for word in ['hello', 'welcome', 'hi']),
            'Capability Overview': lambda text, ctx: any(word in text.lower() for word in ['stock', 'analysis', 'trading', 'market']),
            'Example Commands': lambda text, ctx: 'analyze' in text.lower() or 'scan' in text.lower()
        }
        
        self.test_conversation("Greeting - Hello", "hello", greeting_criteria)
        self.test_conversation("Greeting - Hi", "hi", greeting_criteria)
        self.test_conversation("Greeting - Good Morning", "good morning", greeting_criteria)
        self.test_conversation("Greeting - Hello Predicto", "Hello Predicto", greeting_criteria)
        
        # Test 2: Stock Analysis Requests
        stock_criteria = {
            'Stock Symbol Recognition': lambda text, ctx: any(symbol in text.upper() for symbol in ['AAPL', 'TESLA', 'TSLA']),
            'Analysis Content': lambda text, ctx: any(word in text.lower() for word in ['price', 'analysis', 'stock', 'market'])
        }
        
        self.test_conversation("Stock Analysis - AAPL", "analyze AAPL stock", stock_criteria)
        self.test_conversation("Stock Analysis - Tesla", "what's the sentiment on Tesla?", stock_criteria)
        self.test_conversation("Stock Analysis - Microsoft", "tell me about MSFT", stock_criteria)
        
        # Test 3: Market Scanning Requests
        scan_criteria = {
            'Scanning Response': lambda text, ctx: any(word in text.lower() for word in ['scan', 'opportunities', 'market', 'signals']),
            'TTM Squeeze Reference': lambda text, ctx: 'ttm' in text.lower() or 'squeeze' in text.lower()
        }
        
        self.test_conversation("Market Scan - Opportunities", "scan for opportunities", scan_criteria)
        self.test_conversation("Market Scan - TTM Squeeze", "find TTM Squeeze signals", scan_criteria)
        
        # Test 4: General Trading Questions
        trading_criteria = {
            'Trading Knowledge': lambda text, ctx: any(word in text.lower() for word in ['trading', 'strategy', 'risk', 'portfolio']),
            'Educational Content': lambda text, ctx: len(text) > 100  # Should provide substantial educational content
        }
        
        self.test_conversation("Trading Question - Options", "explain options trading", trading_criteria)
        self.test_conversation("Trading Question - Risk", "how do I manage risk?", trading_criteria)
        
        # Test 5: System Capabilities
        capability_criteria = {
            'Feature List': lambda text, ctx: any(word in text.lower() for word in ['features', 'capabilities', 'can', 'help']),
            'Comprehensive Response': lambda text, ctx: len(text) > 200
        }
        
        self.test_conversation("System Capabilities", "what can you do?", capability_criteria)
        
        # Generate summary
        self.generate_test_summary()
    
    def generate_test_summary(self):
        """Generate comprehensive test summary"""
        print("\n" + "=" * 80)
        print("🔮 COMPREHENSIVE TEST SUMMARY")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result and result['overall_pass'])
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed Tests: {passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        # Response time analysis
        response_times = [result['response_time'] for result in self.test_results if result]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            print(f"Average Response Time: {avg_response_time:.2f}s")
            print(f"Max Response Time: {max_response_time:.2f}s")
        
        print("\n📊 DETAILED RESULTS:")
        for result in self.test_results:
            if result:
                status = "✅ PASS" if result['overall_pass'] else "❌ FAIL"
                print(f"   {status} {result['test_name']} ({result['response_time']:.2f}s)")
        
        print("\n🔍 COMMON ISSUES:")
        issue_counts = {}
        for result in self.test_results:
            if result:
                for criterion, passed in result['evaluation'].items():
                    if not passed:
                        issue_counts[criterion] = issue_counts.get(criterion, 0) + 1
        
        if issue_counts:
            for issue, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"   ❌ {issue}: {count} failures")
        else:
            print("   🎉 No common issues found!")
        
        print("\n" + "=" * 80)
        if success_rate >= 90:
            print("🎉 EXCELLENT! Predicto is performing at production quality!")
        elif success_rate >= 75:
            print("✅ GOOD! Predicto is working well with minor issues to address.")
        elif success_rate >= 50:
            print("⚠️ FAIR! Predicto needs improvement in several areas.")
        else:
            print("❌ POOR! Predicto requires significant fixes before production use.")
        print("=" * 80)

def main():
    """Run comprehensive Predicto testing"""
    tester = PredictoChatTester()
    tester.run_comprehensive_tests()

if __name__ == "__main__":
    main()
