import requests
import json

# Quick final test
try:
    response = requests.post(
        "http://localhost:8080/api/v1/chat",
        json={"message": "hello", "session_id": "final_test"},
        timeout=15
    )
    
    if response.status_code == 200:
        data = response.json()
        response_text = data.get('response', '')
        
        print(f"✅ SUCCESS! Status: {response.status_code}")
        print(f"Response length: {len(response_text)} characters")
        
        if 'initializing' in response_text.lower():
            print("❌ STILL HAS INITIALIZATION MESSAGE")
        else:
            print("✅ NO INITIALIZATION MESSAGE!")
            
        print(f"\nFirst 300 chars: {response_text[:300]}...")
        
    else:
        print(f"❌ Error: {response.status_code}")
        
except Exception as e:
    print(f"❌ Exception: {e}")
