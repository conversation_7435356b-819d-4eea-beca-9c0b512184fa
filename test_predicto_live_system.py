"""
Live System Test for Predicto Functionality
Tests all core Predicto features through the running server
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8080"

def test_health_check():
    """Test basic health check"""
    try:
        response = requests.get(f"{BASE_URL}/api/v1/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health Check: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ Health Check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health Check error: {e}")
        return False

def test_predicto_capabilities():
    """Test Predicto capabilities endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/v1/predicto/capabilities", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Predicto Capabilities:")
            print(f"   Status: {data.get('predicto_status', 'unknown')}")
            print(f"   Total Features: {data.get('natural_language_access', {}).get('total_features', 'unknown')}")
            
            # Show some example conversations
            examples = data.get('natural_language_access', {}).get('conversation_examples', [])
            if examples:
                print(f"   Example Conversations:")
                for example in examples[:3]:
                    print(f"     • {example}")
            
            return True
        else:
            print(f"❌ Predicto Capabilities failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Predicto Capabilities error: {e}")
        return False

def test_predicto_conversation(message, test_name):
    """Test a Predicto conversation"""
    try:
        chat_data = {
            "message": message,
            "session_id": f"test_{test_name.lower().replace(' ', '_')}"
        }
        
        print(f"\n🔮 Testing: {test_name}")
        print(f"   User: {message}")
        
        response = requests.post(
            f"{BASE_URL}/api/v1/chat", 
            json=chat_data,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            predicto_response = data.get('response', 'No response')
            response_type = data.get('type', 'unknown')
            powered_by = data.get('context', {}).get('powered_by', 'unknown')
            
            print(f"   Predicto: {predicto_response[:150]}...")
            print(f"   Type: {response_type}")
            print(f"   Powered by: {powered_by}")
            print(f"✅ {test_name}: SUCCESS")
            return True
        else:
            print(f"❌ {test_name}: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {test_name}: {e}")
        return False

def test_predicto_stock_analysis():
    """Test Predicto stock analysis endpoint"""
    try:
        print(f"\n📊 Testing: Predicto Stock Analysis")
        
        response = requests.post(
            f"{BASE_URL}/api/v1/predicto/analyze",
            json={"symbol": "AAPL"},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            symbol = data.get('symbol', 'unknown')
            powered_by = data.get('powered_by', 'unknown')
            
            print(f"   Symbol: {symbol}")
            print(f"   Powered by: {powered_by}")
            print(f"✅ Predicto Stock Analysis: SUCCESS")
            return True
        else:
            print(f"❌ Predicto Stock Analysis: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Predicto Stock Analysis: {e}")
        return False

def main():
    """Run comprehensive Predicto functionality tests"""
    print("🔮 PREDICTO LIVE SYSTEM FUNCTIONALITY TEST")
    print("=" * 60)
    print(f"Test Time: {datetime.now()}")
    print(f"Server: {BASE_URL}")
    print("=" * 60)
    
    # Basic connectivity test
    print("\n🔗 Basic Connectivity Tests")
    tests_passed = 0
    total_tests = 0
    
    # Health check
    total_tests += 1
    if test_health_check():
        tests_passed += 1
    
    # Predicto capabilities
    total_tests += 1
    if test_predicto_capabilities():
        tests_passed += 1
    
    # Core conversation tests
    print("\n💬 Core Conversation Tests")
    conversation_tests = [
        ("Hello Predicto, what can you do?", "Greeting & Capabilities"),
        ("Analyze AAPL stock", "Stock Analysis"),
        ("What's the sentiment on Tesla?", "Sentiment Analysis"),
        ("Scan for market opportunities", "Market Scanning"),
        ("Predict NVDA price movement", "Price Prediction"),
        ("Explain technical analysis", "Educational Content"),
        ("What are options strategies?", "Options Education"),
        ("How do I manage risk?", "Risk Management"),
        ("Optimize my portfolio", "Portfolio Management"),
        ("Show me TTM Squeeze signals", "TTM Squeeze Detection")
    ]
    
    for message, test_name in conversation_tests:
        total_tests += 1
        if test_predicto_conversation(message, test_name):
            tests_passed += 1
        time.sleep(1)  # Small delay between tests
    
    # Stock analysis endpoint test
    print("\n📊 Specialized Endpoint Tests")
    total_tests += 1
    if test_predicto_stock_analysis():
        tests_passed += 1
    
    # Context retention test
    print("\n🧠 Context Retention Test")
    session_id = "context_test"
    
    # First message
    total_tests += 1
    try:
        response1 = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": "I'm interested in Apple stock", "session_id": session_id},
            timeout=10
        )
        
        # Second message (should remember Apple)
        response2 = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": "What do you think about that stock?", "session_id": session_id},
            timeout=10
        )
        
        if response1.status_code == 200 and response2.status_code == 200:
            print("✅ Context Retention: SUCCESS")
            tests_passed += 1
        else:
            print("❌ Context Retention: FAILED")
    except Exception as e:
        print(f"❌ Context Retention: {e}")
    
    # Natural Language Feature Access Test
    print("\n🎯 Natural Language Feature Access Test")
    feature_tests = [
        ("Get MSFT quote", "Real-time Quotes"),
        ("Set alert for TSLA above $200", "Alert System"),
        ("Morning market briefing", "Proactive Assistant"),
        ("Compare AAPL and GOOGL", "Stock Comparison"),
        ("Best options strategy for earnings", "Options Strategies")
    ]
    
    for message, test_name in feature_tests:
        total_tests += 1
        if test_predicto_conversation(message, test_name):
            tests_passed += 1
        time.sleep(1)
    
    # Results summary
    print("\n" + "=" * 60)
    print("🔮 PREDICTO FUNCTIONALITY TEST RESULTS")
    print("=" * 60)
    
    success_rate = (tests_passed / total_tests) * 100
    print(f"Tests Passed: {tests_passed}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎉 EXCELLENT! Predicto is working great!")
        print("   ✅ Core conversational AI functionality")
        print("   ✅ Stock analysis capabilities")
        print("   ✅ Natural language access to features")
        print("   ✅ Context retention and flow management")
        print("\n💡 Predicto is ready for production use!")
    elif success_rate >= 60:
        print("\n✅ GOOD! Predicto is mostly working")
        print("   Some features may need attention")
    else:
        print("\n⚠️ NEEDS ATTENTION! Several issues detected")
        print("   Please review failed tests")
    
    print("\n🌐 Web Interface Test:")
    print(f"   Open: {BASE_URL}")
    print("   Try the Predicto quick action buttons!")
    print("   Test conversations in the web interface!")
    
    print("=" * 60)
    
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
