"""
Manual Predicto Testing - Individual Test Cases
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_single_conversation(message, test_name):
    """Test a single conversation"""
    print(f"\n{'='*60}")
    print(f"🔮 {test_name}")
    print(f"{'='*60}")
    print(f"Message: '{message}'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": message, "session_id": "manual_test"},
            timeout=15
        )
        response_time = time.time() - start_time
        
        print(f"Status: {response.status_code}")
        print(f"Response Time: {response_time:.2f}s")
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            context = data.get('context', {})
            
            print(f"Response Type: {data.get('type', 'unknown')}")
            print(f"Confidence: {data.get('confidence', 'unknown')}")
            
            # Check branding
            powered_by = context.get('powered_by', 'Not found')
            system = context.get('system', 'Not found')
            print(f"Powered By: {powered_by}")
            print(f"System: {system}")
            
            # Evaluate key criteria
            print(f"\n📊 EVALUATION:")
            print(f"   Correct Branding: {'✅' if 'A.T.L.A.S powered by Predicto' in powered_by else '❌'}")
            print(f"   No Init Message: {'✅' if 'AI systems are currently initializing' not in response_text else '❌'}")
            print(f"   Substantial Response: {'✅' if len(response_text) > 50 else '❌'}")
            print(f"   Predicto Identity: {'✅' if 'Predicto' in response_text else '❌'}")
            print(f"   Professional Tone: {'✅' if not any(word in response_text.lower() for word in ['error', 'failed', 'exception']) else '❌'}")
            
            print(f"\n💬 FULL RESPONSE:")
            print(f"   {response_text}")
            
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"   {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Run manual tests"""
    print("🔮 MANUAL PREDICTO TESTING")
    print("=" * 80)
    
    # Test scenarios
    tests = [
        ("hello", "Greeting Test"),
        ("analyze AAPL", "Stock Analysis Test"),
        ("what can you do?", "Capabilities Test"),
        ("scan for opportunities", "Market Scan Test"),
        ("explain options trading", "Trading Education Test")
    ]
    
    results = []
    for message, test_name in tests:
        success = test_single_conversation(message, test_name)
        results.append((test_name, success))
        time.sleep(2)  # Small delay between tests
    
    # Summary
    print(f"\n{'='*80}")
    print("🔮 TEST SUMMARY")
    print(f"{'='*80}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"Tests Passed: {passed}/{total} ({success_rate:.1f}%)")
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"   {status} {test_name}")
    
    if success_rate >= 90:
        print("\n🎉 EXCELLENT! Predicto is ready for production!")
    elif success_rate >= 75:
        print("\n✅ GOOD! Minor issues to address.")
    else:
        print("\n⚠️ NEEDS IMPROVEMENT! Several issues found.")

if __name__ == "__main__":
    main()
