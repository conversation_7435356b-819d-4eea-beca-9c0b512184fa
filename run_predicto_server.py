"""
Direct Predicto Server Runner
Starts the A.T.L.A.S server with Predicto integration
"""

import os
import sys
import subprocess

# Set validation mode
os.environ["VALIDATION_MODE"] = "true"

print("🔮 Starting A.T.L.A.S with Predicto Integration")
print("=" * 50)
print(f"Validation Mode: {os.environ.get('VALIDATION_MODE')}")
print("=" * 50)

try:
    # Start the server
    print("Starting server...")
    result = subprocess.run([sys.executable, "atlas_server.py"], 
                          capture_output=False, 
                          text=True)
    
except KeyboardInterrupt:
    print("\nServer stopped by user")
except Exception as e:
    print(f"Error starting server: {e}")
