"""
Comprehensive TTM Squeeze Pattern Detection Test
Tests all aspects of the TTM Squeeze algorithm including:
- 3 consecutive decreasing histogram bars + 4th increasing bar
- Momentum confirmation signals
- Multi-timeframe analysis (weekly/daily alignment)
- Squeeze state detection as bonus confirmation
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import TTM components
try:
    from atlas_ttm_pattern_detector import (
        TTMPatternDetector, HistogramPattern, MultiTimeframeAnalysis,
        SqueezeState, TimeFrame, ttm_pattern_integrator
    )
    TTM_AVAILABLE = True
except ImportError as e:
    logger.warning(f"TTM components not available: {e}")
    TTM_AVAILABLE = False


class TTMSqueezeTestSuite:
    """Comprehensive test suite for TTM Squeeze pattern detection"""
    
    def __init__(self):
        self.test_results = []
        self.detector = None
        
    async def initialize(self):
        """Initialize TTM detector"""
        if TTM_AVAILABLE:
            try:
                self.detector = TTMPatternDetector()
                await self.detector.initialize()
                logger.info("✅ TTM Detector initialized for testing")
                return True
            except Exception as e:
                logger.error(f"❌ Failed to initialize TTM detector: {e}")
                return False
        return False
    
    def create_test_histogram_data(self, pattern_type: str) -> List[float]:
        """Create test histogram data for different pattern types"""
        if pattern_type == "valid_bullish":
            # 3 decreasing bars + 1 increasing (bullish signal)
            return [0.5, 0.3, 0.1, 0.4]  # Decreasing then increasing
        
        elif pattern_type == "valid_bearish":
            # 3 decreasing bars + 1 increasing (but negative values for bearish)
            return [-0.1, -0.3, -0.5, -0.2]  # Decreasing magnitude then increasing
        
        elif pattern_type == "invalid_no_decrease":
            # No proper decreasing sequence
            return [0.1, 0.3, 0.5, 0.7]  # All increasing
        
        elif pattern_type == "invalid_no_momentum_shift":
            # Decreasing sequence but no momentum shift
            return [0.5, 0.3, 0.1, 0.05]  # Continues decreasing
        
        elif pattern_type == "weak_signal":
            # Barely meets criteria
            return [0.12, 0.11, 0.10, 0.101]  # Very small changes
        
        elif pattern_type == "strong_signal":
            # Strong clear pattern
            return [0.8, 0.4, 0.1, 0.6]  # Clear decrease then strong increase
        
        else:
            return [0.0, 0.0, 0.0, 0.0]
    
    def test_histogram_pattern_detection(self) -> Dict[str, Any]:
        """Test histogram pattern detection logic"""
        logger.info("🧪 Testing histogram pattern detection...")
        
        test_cases = [
            ("valid_bullish", True, "Should detect valid bullish pattern"),
            ("valid_bearish", True, "Should detect valid bearish pattern"),
            ("invalid_no_decrease", False, "Should reject pattern without decreasing sequence"),
            ("invalid_no_momentum_shift", False, "Should reject pattern without momentum shift"),
            ("weak_signal", True, "Should detect weak but valid pattern"),
            ("strong_signal", True, "Should detect strong pattern with high confidence")
        ]
        
        results = []
        for pattern_type, expected_valid, description in test_cases:
            histogram_data = self.create_test_histogram_data(pattern_type)
            
            # Create histogram pattern object
            pattern = HistogramPattern(
                bars=histogram_data,
                decreasing_sequence=False,  # Will be calculated in __post_init__
                momentum_shift=False,       # Will be calculated in __post_init__
                momentum_strength=0.0,     # Will be calculated in __post_init__
                pattern_confidence=0.0     # Will be calculated in __post_init__
            )
            
            # Check if pattern is valid
            is_valid = pattern.decreasing_sequence and pattern.momentum_shift
            confidence = pattern.pattern_confidence
            
            # Evaluate result
            test_passed = (is_valid == expected_valid)
            
            result = {
                "test_case": pattern_type,
                "description": description,
                "histogram_data": histogram_data,
                "decreasing_sequence": pattern.decreasing_sequence,
                "momentum_shift": pattern.momentum_shift,
                "momentum_strength": pattern.momentum_strength,
                "confidence": confidence,
                "expected_valid": expected_valid,
                "actual_valid": is_valid,
                "test_passed": test_passed,
                "status": "✅ PASS" if test_passed else "❌ FAIL"
            }
            
            results.append(result)
            logger.info(f"{result['status']} {pattern_type}: {description}")
            if not test_passed:
                logger.warning(f"   Expected: {expected_valid}, Got: {is_valid}")
        
        # Calculate summary
        passed_tests = sum(1 for r in results if r["test_passed"])
        total_tests = len(results)
        
        return {
            "test_name": "Histogram Pattern Detection",
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": passed_tests / total_tests,
            "results": results
        }
    
    def test_confidence_scoring(self) -> Dict[str, Any]:
        """Test confidence scoring algorithm"""
        logger.info("🧪 Testing confidence scoring...")
        
        test_cases = [
            {
                "name": "strong_pattern",
                "bars": [1.0, 0.5, 0.1, 0.8],
                "expected_confidence_range": (0.7, 1.0),
                "description": "Strong pattern should have high confidence"
            },
            {
                "name": "weak_pattern", 
                "bars": [0.11, 0.10, 0.09, 0.095],
                "expected_confidence_range": (0.3, 0.6),
                "description": "Weak pattern should have moderate confidence"
            },
            {
                "name": "inconsistent_pattern",
                "bars": [1.0, 0.1, 0.9, 0.95],
                "expected_confidence_range": (0.2, 0.5),
                "description": "Inconsistent pattern should have low confidence"
            }
        ]
        
        results = []
        for test_case in test_cases:
            pattern = HistogramPattern(
                bars=test_case["bars"],
                decreasing_sequence=False,
                momentum_shift=False,
                momentum_strength=0.0,
                pattern_confidence=0.0
            )
            
            confidence = pattern.pattern_confidence
            min_expected, max_expected = test_case["expected_confidence_range"]
            
            confidence_in_range = min_expected <= confidence <= max_expected
            
            result = {
                "test_case": test_case["name"],
                "description": test_case["description"],
                "bars": test_case["bars"],
                "confidence": confidence,
                "expected_range": test_case["expected_confidence_range"],
                "in_expected_range": confidence_in_range,
                "status": "✅ PASS" if confidence_in_range else "❌ FAIL"
            }
            
            results.append(result)
            logger.info(f"{result['status']} {test_case['name']}: Confidence {confidence:.3f}")
            
            if not confidence_in_range:
                logger.warning(f"   Expected: {min_expected:.2f}-{max_expected:.2f}, Got: {confidence:.3f}")
        
        passed_tests = sum(1 for r in results if r["in_expected_range"])
        total_tests = len(results)
        
        return {
            "test_name": "Confidence Scoring",
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": passed_tests / total_tests,
            "results": results
        }
    
    async def test_multi_timeframe_analysis(self) -> Dict[str, Any]:
        """Test multi-timeframe analysis"""
        logger.info("🧪 Testing multi-timeframe analysis...")
        
        # Create test patterns for daily and weekly
        daily_pattern = HistogramPattern(
            bars=[0.5, 0.3, 0.1, 0.4],  # Valid bullish pattern
            decreasing_sequence=False,
            momentum_shift=False,
            momentum_strength=0.0,
            pattern_confidence=0.0
        )
        
        weekly_pattern = HistogramPattern(
            bars=[0.8, 0.4, 0.2, 0.6],  # Valid bullish pattern (aligned)
            decreasing_sequence=False,
            momentum_shift=False,
            momentum_strength=0.0,
            pattern_confidence=0.0
        )
        
        # Test aligned timeframes
        aligned_analysis = MultiTimeframeAnalysis(
            daily_pattern=daily_pattern,
            weekly_pattern=weekly_pattern,
            timeframe_alignment=False,  # Will be calculated
            combined_confidence=0.0,    # Will be calculated
            trend_direction=""          # Will be calculated
        )
        
        # Test misaligned timeframes (opposite directions)
        weekly_bearish = HistogramPattern(
            bars=[-0.2, -0.4, -0.8, -0.6],  # Valid bearish pattern
            decreasing_sequence=False,
            momentum_shift=False,
            momentum_strength=0.0,
            pattern_confidence=0.0
        )
        
        misaligned_analysis = MultiTimeframeAnalysis(
            daily_pattern=daily_pattern,      # Bullish
            weekly_pattern=weekly_bearish,    # Bearish
            timeframe_alignment=False,
            combined_confidence=0.0,
            trend_direction=""
        )
        
        results = [
            {
                "test_case": "aligned_timeframes",
                "description": "Daily and weekly patterns aligned (both bullish)",
                "daily_valid": daily_pattern.decreasing_sequence and daily_pattern.momentum_shift,
                "weekly_valid": weekly_pattern.decreasing_sequence and weekly_pattern.momentum_shift,
                "alignment": aligned_analysis.timeframe_alignment,
                "combined_confidence": aligned_analysis.combined_confidence,
                "trend_direction": aligned_analysis.trend_direction,
                "expected_alignment": True,
                "test_passed": aligned_analysis.timeframe_alignment == True
            },
            {
                "test_case": "misaligned_timeframes", 
                "description": "Daily bullish, weekly bearish (misaligned)",
                "daily_valid": daily_pattern.decreasing_sequence and daily_pattern.momentum_shift,
                "weekly_valid": weekly_bearish.decreasing_sequence and weekly_bearish.momentum_shift,
                "alignment": misaligned_analysis.timeframe_alignment,
                "combined_confidence": misaligned_analysis.combined_confidence,
                "trend_direction": misaligned_analysis.trend_direction,
                "expected_alignment": False,
                "test_passed": misaligned_analysis.timeframe_alignment == False
            }
        ]
        
        for result in results:
            status = "✅ PASS" if result["test_passed"] else "❌ FAIL"
            logger.info(f"{status} {result['test_case']}: {result['description']}")
            result["status"] = status
        
        passed_tests = sum(1 for r in results if r["test_passed"])
        total_tests = len(results)
        
        return {
            "test_name": "Multi-Timeframe Analysis",
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": passed_tests / total_tests,
            "results": results
        }


async def run_comprehensive_ttm_tests():
    """Run all TTM Squeeze tests"""
    logger.info("🚀 Starting Comprehensive TTM Squeeze Pattern Detection Tests")
    logger.info("=" * 70)
    
    test_suite = TTMSqueezeTestSuite()
    
    # Initialize test suite
    if not await test_suite.initialize():
        logger.error("❌ Failed to initialize test suite")
        return
    
    # Run all tests
    all_results = []
    
    # Test 1: Histogram Pattern Detection
    result1 = test_suite.test_histogram_pattern_detection()
    all_results.append(result1)
    
    # Test 2: Confidence Scoring
    result2 = test_suite.test_confidence_scoring()
    all_results.append(result2)
    
    # Test 3: Multi-timeframe Analysis
    result3 = await test_suite.test_multi_timeframe_analysis()
    all_results.append(result3)
    
    # Generate summary report
    logger.info("\n" + "=" * 70)
    logger.info("📊 TTM SQUEEZE PATTERN DETECTION TEST SUMMARY")
    logger.info("=" * 70)
    
    total_tests = sum(r["total_tests"] for r in all_results)
    total_passed = sum(r["passed_tests"] for r in all_results)
    overall_success_rate = total_passed / total_tests if total_tests > 0 else 0
    
    for result in all_results:
        logger.info(f"{result['test_name']}: {result['passed_tests']}/{result['total_tests']} "
                   f"({result['success_rate']*100:.1f}%)")
    
    logger.info(f"\n🎯 OVERALL RESULTS: {total_passed}/{total_tests} ({overall_success_rate*100:.1f}%)")
    
    if overall_success_rate >= 0.8:
        logger.info("✅ TTM Squeeze Pattern Detection: VALIDATION SUCCESSFUL")
    else:
        logger.warning("⚠️ TTM Squeeze Pattern Detection: NEEDS IMPROVEMENT")
    
    return all_results


if __name__ == "__main__":
    asyncio.run(run_comprehensive_ttm_tests())
