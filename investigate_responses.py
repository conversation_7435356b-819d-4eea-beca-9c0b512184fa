"""
Thorough Investigation of Predicto Response Content
This will examine the actual response text, not just status codes
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_and_examine_response(message, test_name):
    """Test a conversation and examine the full response content"""
    print(f"\n{'='*80}")
    print(f"🔍 INVESTIGATING: {test_name}")
    print(f"{'='*80}")
    print(f"📤 INPUT MESSAGE: '{message}'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": message, "session_id": "investigation"},
            timeout=20
        )
        response_time = time.time() - start_time
        
        print(f"⏱️  Response Time: {response_time:.2f}s")
        print(f"📊 HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            context = data.get('context', {})
            
            print(f"📋 Response Type: {data.get('type', 'unknown')}")
            print(f"🎯 Confidence: {data.get('confidence', 'unknown')}")
            
            # Check branding
            powered_by = context.get('powered_by', 'Not found')
            system = context.get('system', 'Not found')
            print(f"🏷️  Powered By: {powered_by}")
            print(f"🏷️  System: {system}")
            
            print(f"\n📝 FULL RESPONSE TEXT:")
            print(f"{'─'*80}")
            print(response_text)
            print(f"{'─'*80}")
            
            # Analyze response content
            print(f"\n🔍 RESPONSE ANALYSIS:")
            
            # Check for initialization message
            has_init_message = "initializing" in response_text.lower() or "AI systems are currently initializing" in response_text
            print(f"   ❌ Contains Initialization Message: {'YES' if has_init_message else 'NO'}")
            
            # Check for proper greeting response
            is_greeting = any(word in message.lower() for word in ['hello', 'hi', 'hey', 'good'])
            has_welcome = any(word in response_text.lower() for word in ['hello', 'welcome', 'hi', 'predicto'])
            if is_greeting:
                print(f"   {'✅' if has_welcome else '❌'} Proper Greeting Response: {'YES' if has_welcome else 'NO'}")
            
            # Check for stock analysis content
            is_stock_request = any(word in message.lower() for word in ['analyze', 'aapl', 'stock', 'tesla'])
            has_analysis = any(word in response_text.lower() for word in ['analysis', 'price', 'stock', 'market', 'trading'])
            if is_stock_request:
                print(f"   {'✅' if has_analysis else '❌'} Contains Analysis Content: {'YES' if has_analysis else 'NO'}")
            
            # Check for capability information
            is_capability_request = any(phrase in message.lower() for phrase in ['what can you do', 'capabilities', 'help'])
            has_capabilities = any(word in response_text.lower() for word in ['can help', 'capabilities', 'features', 'analysis'])
            if is_capability_request:
                print(f"   {'✅' if has_capabilities else '❌'} Shows Capabilities: {'YES' if has_capabilities else 'NO'}")
            
            # Check response length
            response_length = len(response_text)
            print(f"   {'✅' if response_length > 100 else '❌'} Substantial Response: {response_length} characters")
            
            # Check for error indicators
            has_errors = any(word in response_text.lower() for word in ['error', 'failed', 'exception', 'traceback'])
            print(f"   {'✅' if not has_errors else '❌'} No Error Messages: {'YES' if not has_errors else 'NO'}")
            
            # Overall assessment
            if has_init_message:
                print(f"\n🚨 ISSUE FOUND: Response contains initialization message!")
                print(f"   This suggests the system thinks it's still initializing.")
            elif response_length < 50:
                print(f"\n⚠️  ISSUE FOUND: Response too short ({response_length} chars)")
            elif has_errors:
                print(f"\n❌ ISSUE FOUND: Response contains error messages")
            else:
                print(f"\n✅ RESPONSE QUALITY: Good")
            
            return {
                'message': message,
                'response_text': response_text,
                'has_init_message': has_init_message,
                'response_length': response_length,
                'has_errors': has_errors,
                'status_code': response.status_code,
                'response_time': response_time
            }
            
        else:
            print(f"❌ HTTP ERROR: {response.status_code}")
            print(f"   Error Details: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return None

def main():
    """Run comprehensive response investigation"""
    print("🔍 COMPREHENSIVE PREDICTO RESPONSE INVESTIGATION")
    print("=" * 100)
    print("This will examine the actual response content to identify issues")
    print("=" * 100)
    
    # Test scenarios to investigate
    test_scenarios = [
        ("hello", "Basic Greeting"),
        ("hi there", "Casual Greeting"),
        ("good morning", "Formal Greeting"),
        ("Hello Predicto", "Direct Predicto Greeting"),
        ("analyze AAPL", "Stock Analysis Request"),
        ("what's the sentiment on Tesla?", "Sentiment Analysis"),
        ("tell me about MSFT", "Stock Information"),
        ("what can you do?", "Capability Inquiry"),
        ("help me", "Help Request"),
        ("scan for opportunities", "Market Scanning"),
        ("find TTM Squeeze signals", "Technical Analysis"),
        ("explain options trading", "Educational Request"),
        ("how do I manage risk?", "Risk Management"),
        ("optimize my portfolio", "Portfolio Request"),
        ("what's happening in the market?", "Market Overview")
    ]
    
    results = []
    issues_found = []
    
    for message, test_name in test_scenarios:
        result = test_and_examine_response(message, test_name)
        if result:
            results.append(result)
            
            # Track issues
            if result['has_init_message']:
                issues_found.append(f"Initialization message in: '{message}'")
            elif result['response_length'] < 50:
                issues_found.append(f"Short response for: '{message}' ({result['response_length']} chars)")
            elif result['has_errors']:
                issues_found.append(f"Error in response to: '{message}'")
        
        time.sleep(2)  # Small delay between tests
    
    # Generate comprehensive summary
    print(f"\n{'='*100}")
    print("🔍 INVESTIGATION SUMMARY")
    print(f"{'='*100}")
    
    total_tests = len(results)
    init_message_count = sum(1 for r in results if r['has_init_message'])
    error_count = sum(1 for r in results if r['has_errors'])
    short_response_count = sum(1 for r in results if r['response_length'] < 50)
    
    print(f"📊 STATISTICS:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Initialization Messages: {init_message_count}")
    print(f"   Error Responses: {error_count}")
    print(f"   Short Responses: {short_response_count}")
    
    if results:
        avg_response_time = sum(r['response_time'] for r in results) / len(results)
        avg_response_length = sum(r['response_length'] for r in results) / len(results)
        print(f"   Average Response Time: {avg_response_time:.2f}s")
        print(f"   Average Response Length: {avg_response_length:.0f} characters")
    
    print(f"\n🚨 ISSUES IDENTIFIED:")
    if issues_found:
        for issue in issues_found:
            print(f"   ❌ {issue}")
    else:
        print(f"   ✅ No major issues found!")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    if init_message_count > 0:
        print(f"   🔧 Fix initialization message logic - {init_message_count} responses showing false init")
        print(f"   🔍 Check AI engine status detection in orchestrator")
        print(f"   🔍 Verify validation mode engine status setting")
    elif short_response_count > 0:
        print(f"   📝 Improve response content - {short_response_count} responses too short")
    elif error_count > 0:
        print(f"   🛠️  Fix error handling - {error_count} responses contain errors")
    else:
        print(f"   🎉 System appears to be working correctly!")
    
    print(f"\n{'='*100}")

if __name__ == "__main__":
    main()
