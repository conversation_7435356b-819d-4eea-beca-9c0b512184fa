"""
Quick Predicto Test - Test key functionality
"""

import requests
import json

BASE_URL = "http://localhost:8080"

def test_predicto_chat():
    """Test Predicto chat"""
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": "Hello Predicto!", "session_id": "quick_test"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Predicto Chat: Working")
            print(f"   Response: {data.get('response', 'No response')[:100]}...")
            return True
        else:
            print(f"❌ Predicto Chat: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Predicto Chat: {e}")
        return False

def test_predicto_capabilities():
    """Test Predicto capabilities"""
    try:
        response = requests.get(f"{BASE_URL}/api/v1/predicto/capabilities", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Predicto Capabilities: Working")
            print(f"   Status: {data.get('predicto_status', 'unknown')}")
            print(f"   Features: {data.get('natural_language_access', {}).get('total_features', 'unknown')}")
            return True
        else:
            print(f"❌ Predicto Capabilities: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Predicto Capabilities: {e}")
        return False

def test_predicto_stock_analysis():
    """Test Predicto stock analysis (fixed endpoint)"""
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/predicto/analyze",
            json={"symbol": "AAPL"},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Predicto Stock Analysis: Working")
            print(f"   Symbol: {data.get('symbol', 'unknown')}")
            print(f"   Powered by: {data.get('powered_by', 'unknown')}")
            return True
        else:
            print(f"❌ Predicto Stock Analysis: HTTP {response.status_code}")
            if response.status_code == 500:
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('detail', 'Unknown error')}")
                except:
                    print(f"   Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Predicto Stock Analysis: {e}")
        return False

def main():
    """Run quick tests"""
    print("🔮 PREDICTO QUICK FUNCTIONALITY TEST")
    print("=" * 40)
    
    tests = [
        ("Predicto Chat", test_predicto_chat),
        ("Predicto Capabilities", test_predicto_capabilities),
        ("Predicto Stock Analysis", test_predicto_stock_analysis)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
    
    print(f"\n{'='*40}")
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All core Predicto features working!")
    else:
        print("⚠️ Some issues detected")

if __name__ == "__main__":
    main()
