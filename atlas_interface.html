<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Predicto - AI Stock Analysis Expert | A.T.L.A.S</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(0, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            background: linear-gradient(45deg, #00ffff, #0080ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: #b0b0b0;
            font-size: 1.1em;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background: rgba(0, 255, 255, 0.1);
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid rgba(0, 255, 255, 0.3);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #00ff00;
            animation: pulse 2s infinite;
        }

        .status-dot.initializing {
            background: #ffaa00;
        }

        .status-dot.error {
            background: #ff4444;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: 1px solid rgba(0, 255, 255, 0.2);
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background: rgba(0, 128, 255, 0.2);
            border: 1px solid rgba(0, 128, 255, 0.4);
            margin-left: auto;
            text-align: right;
        }

        .message.assistant {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            margin-right: auto;
        }

        .message.system {
            background: rgba(255, 165, 0, 0.1);
            border: 1px solid rgba(255, 165, 0, 0.3);
            margin: 0 auto;
            text-align: center;
            font-style: italic;
        }

        .message-header {
            font-size: 0.9em;
            color: #888;
            margin-bottom: 5px;
        }

        .message-content {
            line-height: 1.6;
        }

        .chat-input-container {
            padding: 20px;
            border-top: 1px solid rgba(0, 255, 255, 0.2);
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 10px;
            color: #ffffff;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .chat-input:focus {
            border-color: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }

        .chat-input::placeholder {
            color: #888;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(45deg, #00ffff, #0080ff);
            border: none;
            border-radius: 10px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 255, 0.4);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 15px;
            color: #888;
            font-style: italic;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots::after {
            content: '';
            animation: typing 1.5s infinite;
        }

        @keyframes typing {
            0%, 20% { content: '.'; }
            40% { content: '..'; }
            60%, 100% { content: '...'; }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #0080ff);
            width: 0%;
            transition: width 0.3s ease;
        }

        /* Scrollbar styling */
        .chat-messages::-webkit-scrollbar {
            width: 8px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 255, 255, 0.3);
            border-radius: 4px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 255, 255, 0.5);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .message {
                max-width: 95%;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* Predicto Quick Actions Styling */
        .quick-action-btn {
            padding: 8px 12px;
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 20px;
            color: #00ffff;
            font-size: 0.85em;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .quick-action-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: rgba(0, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 255, 255, 0.2);
        }

        .quick-action-btn:active {
            transform: translateY(0);
        }

        .predicto-panel {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔮 Predicto</h1>
            <p>AI Stock Analysis Expert • Powered by A.T.L.A.S</p>
            <div style="margin-top: 10px; font-size: 0.9em; color: #00ffff;">
                Natural language access to 25+ advanced trading capabilities
            </div>
        </div>

        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot initializing" id="statusDot"></div>
                <span id="statusText">Initializing...</span>
            </div>
            <div id="initializationProgress" style="display: none;">
                <div>Initialization Progress</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
            <div id="systemInfo">
                <span id="versionInfo">v4.0.0</span>
            </div>
        </div>

        <!-- Predicto Quick Actions Panel -->
        <div class="predicto-panel" style="margin-bottom: 15px; padding: 15px; background: rgba(0, 255, 255, 0.05); border-radius: 10px; border: 1px solid rgba(0, 255, 255, 0.2);">
            <div style="font-size: 0.9em; color: #00ffff; margin-bottom: 10px; font-weight: bold;">🔮 Quick Actions</div>
            <div class="quick-actions" style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="quick-action-btn" onclick="sendQuickMessage('Analyze AAPL')">📊 Analyze Stock</button>
                <button class="quick-action-btn" onclick="sendQuickMessage('Scan for TTM Squeeze signals')">🔍 Market Scan</button>
                <button class="quick-action-btn" onclick="sendQuickMessage('What is the market sentiment today?')">📈 Market Sentiment</button>
                <button class="quick-action-btn" onclick="sendQuickMessage('Explain options trading basics')">🎓 Learn Trading</button>
                <button class="quick-action-btn" onclick="sendQuickMessage('Show me portfolio optimization strategies')">💼 Portfolio Help</button>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message system">
                    <div class="message-content">
                        <strong>🔮 Welcome to Predicto!</strong><br><br>
                        I'm your AI stock analysis expert with access to 25+ advanced A.T.L.A.S capabilities. I can help you with:<br>
                        • <strong>Stock Analysis</strong> - Technical & fundamental analysis<br>
                        • <strong>Market Scanning</strong> - TTM Squeeze & opportunity detection<br>
                        • <strong>Predictions</strong> - LSTM neural networks & forecasting<br>
                        • <strong>Options Trading</strong> - Greeks & strategy analysis<br>
                        • <strong>Portfolio Optimization</strong> - Risk management & allocation<br>
                        • <strong>Education</strong> - Trading concepts from 5 integrated books<br><br>
                        <em>Try asking: "Analyze AAPL" or "Scan for strong signals" or "Explain options trading"</em>
                    </div>
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                <span class="typing-dots">Predicto is analyzing</span>
            </div>

            <div class="chat-input-container">
                <form class="chat-input-form" id="chatForm">
                    <input 
                        type="text" 
                        class="chat-input" 
                        id="chatInput" 
                        placeholder="Ask Predicto: 'Analyze AAPL', 'Scan for opportunities', 'Explain options'..."
                        autocomplete="off"
                    >
                    <button type="submit" class="send-button" id="sendButton">Send</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        class AtlasInterface {
            constructor() {
                this.chatMessages = document.getElementById('chatMessages');
                this.chatInput = document.getElementById('chatInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatForm = document.getElementById('chatForm');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.statusDot = document.getElementById('statusDot');
                this.statusText = document.getElementById('statusText');
                this.progressFill = document.getElementById('progressFill');
                this.initializationProgress = document.getElementById('initializationProgress');
                
                this.sessionId = this.generateSessionId();
                this.isTyping = false;
                
                this.init();
            }

            init() {
                this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
                this.chatInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.handleSubmit(e);
                    }
                });

                // Start health monitoring
                this.startHealthMonitoring();
                
                // Focus input
                this.chatInput.focus();
            }

            generateSessionId() {
                return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            async handleSubmit(e) {
                e.preventDefault();
                
                const message = this.chatInput.value.trim();
                if (!message || this.isTyping) return;

                this.addMessage(message, 'user');
                this.chatInput.value = '';
                this.setTyping(true);

                try {
                    const response = await this.sendMessage(message);
                    this.addMessage(response.response, 'assistant', response);
                } catch (error) {
                    this.addMessage('Sorry, I encountered an error. Please try again.', 'assistant', { type: 'error' });
                    console.error('Chat error:', error);
                } finally {
                    this.setTyping(false);
                }
            }

            async sendMessage(message) {
                const response = await fetch('/api/v1/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: this.sessionId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            }

            addMessage(content, sender, metadata = {}) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                const timestamp = new Date().toLocaleTimeString();
                const senderName = sender === 'user' ? 'You' : 'Predicto';
                
                messageDiv.innerHTML = `
                    <div class="message-header">${senderName} • ${timestamp}</div>
                    <div class="message-content">${this.formatMessage(content, metadata)}</div>
                `;

                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            formatMessage(content, metadata) {
                // Basic markdown-like formatting
                content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
                content = content.replace(/\n/g, '<br>');
                
                // Add context information for certain message types
                if (metadata.type === 'system_status') {
                    content = `🔧 ${content}`;
                } else if (metadata.type === 'education') {
                    content = `📚 ${content}`;
                } else if (metadata.type === 'trading_analysis') {
                    content = `📊 ${content}`;
                }
                
                return content;
            }

            setTyping(isTyping) {
                this.isTyping = isTyping;
                this.sendButton.disabled = isTyping;
                this.typingIndicator.style.display = isTyping ? 'block' : 'none';
                
                if (isTyping) {
                    this.scrollToBottom();
                }
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }

            async startHealthMonitoring() {
                const checkHealth = async () => {
                    try {
                        const response = await fetch('/api/v1/health');
                        const health = await response.json();
                        
                        this.updateStatus(health);
                        
                        // Check initialization progress
                        if (health.initialization_progress) {
                            this.updateInitializationProgress(health.initialization_progress);
                        }
                        
                    } catch (error) {
                        this.updateStatus({ status: 'error' });
                        console.error('Health check failed:', error);
                    }
                };

                // Initial check
                await checkHealth();
                
                // Regular health checks
                setInterval(checkHealth, 5000);
            }

            updateStatus(health) {
                const { status } = health;
                
                this.statusDot.className = `status-dot ${status}`;
                
                const statusMessages = {
                    'healthy': '✅ System Ready',
                    'initializing': '🔧 Initializing...',
                    'degraded': '⚠️ Limited Functionality',
                    'error': '❌ System Error'
                };
                
                this.statusText.textContent = statusMessages[status] || 'Unknown Status';
            }

            updateInitializationProgress(progress) {
                if (progress.overall !== undefined) {
                    const percentage = Math.round(progress.overall * 100);
                    this.progressFill.style.width = `${percentage}%`;
                    
                    if (percentage < 100) {
                        this.initializationProgress.style.display = 'block';
                    } else {
                        this.initializationProgress.style.display = 'none';
                    }
                }
            }
        }

        // Global function for quick actions
        function sendQuickMessage(message) {
            const messageInput = document.getElementById('messageInput');
            const chatForm = document.getElementById('chatForm');

            // Set the message in the input field
            messageInput.value = message;

            // Trigger the form submission
            if (chatForm) {
                chatForm.dispatchEvent(new Event('submit'));
            }
        }

        // Initialize the interface when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new AtlasInterface();
        });
    </script>
</body>
</html>
