#!/usr/bin/env python3
"""
Predicto Startup Validation Script
Tests the A.T.L.A.S system with Predicto integration
"""

import os
import sys
import asyncio
import traceback
from datetime import datetime

# Set validation mode before any imports
os.environ["VALIDATION_MODE"] = "true"

def print_header():
    """Print startup header"""
    print("🔮 A.T.L.A.S PREDICTO INTEGRATION VALIDATION")
    print("=" * 60)
    print(f"Timestamp: {datetime.now()}")
    print(f"Validation Mode: {os.environ.get('VALIDATION_MODE', 'false')}")
    print(f"Python Version: {sys.version}")
    print("=" * 60)

def test_basic_imports():
    """Test basic system imports"""
    print("\n📦 Testing Basic Imports...")
    
    try:
        print("  Importing config...")
        import config
        print(f"  ✅ Config imported - Validation: {config.settings.VALIDATION_MODE}")
        
        print("  Importing models...")
        import models
        print("  ✅ Models imported")
        
        return True
    except Exception as e:
        print(f"  ❌ Basic import failed: {e}")
        traceback.print_exc()
        return False

def test_predicto_imports():
    """Test Predicto component imports"""
    print("\n🔮 Testing Predicto Component Imports...")
    
    components = [
        ("Predicto Engine", "atlas_predicto_engine"),
        ("Stock Intelligence Hub", "atlas_stock_intelligence_hub"),
        ("Unified Access Layer", "atlas_unified_access_layer"),
        ("Conversation Flow Manager", "atlas_conversation_flow_manager")
    ]
    
    success_count = 0
    
    for name, module_name in components:
        try:
            print(f"  Importing {name}...")
            __import__(module_name)
            print(f"  ✅ {name} imported successfully")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {name} import failed: {e}")
            traceback.print_exc()
    
    print(f"\n  📊 Predicto Components: {success_count}/{len(components)} imported successfully")
    return success_count == len(components)

def test_ai_engine_import():
    """Test AI Engine with Predicto integration"""
    print("\n🤖 Testing AI Engine Import...")
    
    try:
        print("  Importing AI Engine...")
        from atlas_ai_engine import AtlasAIEngine
        print("  ✅ AI Engine imported")
        
        print("  Creating AI Engine instance...")
        ai_engine = AtlasAIEngine()
        print("  ✅ AI Engine instance created")
        
        # Check for Predicto integration attributes
        predicto_attrs = [
            'predicto_engine',
            'stock_intelligence_hub', 
            'unified_access_layer',
            'conversation_flow_manager'
        ]
        
        for attr in predicto_attrs:
            if hasattr(ai_engine, attr):
                print(f"  ✅ Has {attr} attribute")
            else:
                print(f"  ⚠️  Missing {attr} attribute")
        
        return True
    except Exception as e:
        print(f"  ❌ AI Engine test failed: {e}")
        traceback.print_exc()
        return False

def test_server_import():
    """Test server import"""
    print("\n🌐 Testing Server Import...")
    
    try:
        print("  Importing server...")
        import atlas_server
        print("  ✅ Server imported successfully")
        
        # Check if FastAPI app exists
        if hasattr(atlas_server, 'app'):
            print("  ✅ FastAPI app found")
        else:
            print("  ⚠️  FastAPI app not found")
        
        return True
    except Exception as e:
        print(f"  ❌ Server import failed: {e}")
        traceback.print_exc()
        return False

async def test_predicto_initialization():
    """Test Predicto component initialization"""
    print("\n🔮 Testing Predicto Initialization...")
    
    try:
        from atlas_predicto_engine import PredictoConversationalEngine
        
        print("  Creating Predicto Engine...")
        predicto = PredictoConversationalEngine()
        print("  ✅ Predicto Engine created")
        
        print("  Initializing Predicto Engine...")
        await predicto.initialize()
        print(f"  ✅ Predicto Engine initialized - Status: {predicto.status}")
        
        print("  Testing basic conversation processing...")
        # Create a mock orchestrator for testing
        class MockOrchestrator:
            pass
        
        mock_orch = MockOrchestrator()
        response = await predicto.process_conversation(
            "Hello Predicto", "test_session", mock_orch
        )
        
        if response and hasattr(response, 'response'):
            print("  ✅ Basic conversation processing works")
        else:
            print("  ⚠️  Conversation processing returned unexpected result")
        
        await predicto.cleanup()
        print("  ✅ Predicto Engine cleanup completed")
        
        return True
    except Exception as e:
        print(f"  ❌ Predicto initialization failed: {e}")
        traceback.print_exc()
        return False

async def test_ai_engine_initialization():
    """Test AI Engine initialization with Predicto"""
    print("\n🤖 Testing AI Engine Initialization...")
    
    try:
        from atlas_ai_engine import AtlasAIEngine
        
        print("  Creating AI Engine...")
        ai_engine = AtlasAIEngine()
        print("  ✅ AI Engine created")
        
        print("  Initializing AI Engine...")
        await ai_engine.initialize()
        print(f"  ✅ AI Engine initialized - Status: {ai_engine.status}")
        
        # Check Predicto components
        if ai_engine.predicto_engine:
            print(f"  ✅ Predicto Engine integrated - Status: {ai_engine.predicto_engine.status}")
        else:
            print("  ⚠️  Predicto Engine not integrated")
        
        if ai_engine.stock_intelligence_hub:
            print(f"  ✅ Stock Intelligence Hub integrated - Status: {ai_engine.stock_intelligence_hub.status}")
        else:
            print("  ⚠️  Stock Intelligence Hub not integrated")
        
        await ai_engine.cleanup()
        print("  ✅ AI Engine cleanup completed")
        
        return True
    except Exception as e:
        print(f"  ❌ AI Engine initialization failed: {e}")
        traceback.print_exc()
        return False

def print_summary(results):
    """Print test summary"""
    print("\n" + "=" * 60)
    print("🔮 PREDICTO VALIDATION SUMMARY")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"\nTest Results: {passed_tests}/{total_tests} passed")
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED!")
        print("Predicto integration is working correctly.")
        print("\nNext steps:")
        print("1. Start the server: python atlas_server.py")
        print("2. Open atlas_interface.html in your browser")
        print("3. Test Predicto conversations!")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} tests failed.")
        print("Please review the errors above and fix the issues.")
    
    print("=" * 60)

async def main():
    """Run all validation tests"""
    print_header()
    
    # Synchronous tests
    results = {
        "Basic Imports": test_basic_imports(),
        "Predicto Imports": test_predicto_imports(),
        "AI Engine Import": test_ai_engine_import(),
        "Server Import": test_server_import()
    }
    
    # Asynchronous tests
    async_results = {
        "Predicto Initialization": await test_predicto_initialization(),
        "AI Engine Initialization": await test_ai_engine_initialization()
    }
    
    results.update(async_results)
    
    print_summary(results)
    
    # Return success status
    return all(results.values())

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Validation failed with unexpected error: {e}")
        traceback.print_exc()
        sys.exit(1)
