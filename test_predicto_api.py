"""
Test Predicto API endpoints
"""

import requests
import json
import time

def test_health_endpoint():
    """Test health endpoint"""
    try:
        response = requests.get("http://localhost:8080/api/v1/health", timeout=5)
        print(f"Health endpoint: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
            return True
    except Exception as e:
        print(f"Health endpoint failed: {e}")
    return False

def test_predicto_capabilities():
    """Test Predicto capabilities endpoint"""
    try:
        response = requests.get("http://localhost:8080/api/v1/predicto/capabilities", timeout=5)
        print(f"Predicto capabilities: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Predicto status: {data.get('predicto_status', 'unknown')}")
            print(f"Total features: {data.get('natural_language_access', {}).get('total_features', 'unknown')}")
            return True
    except Exception as e:
        print(f"Predicto capabilities failed: {e}")
    return False

def test_predicto_chat():
    """Test Predicto chat endpoint"""
    try:
        chat_data = {
            "message": "Hello Predicto, what can you do?",
            "session_id": "test_session"
        }
        
        response = requests.post(
            "http://localhost:8080/api/v1/chat", 
            json=chat_data,
            timeout=10
        )
        
        print(f"Predicto chat: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data.get('response', 'No response')[:100]}...")
            print(f"Type: {data.get('type', 'unknown')}")
            print(f"Powered by: {data.get('context', {}).get('powered_by', 'unknown')}")
            return True
    except Exception as e:
        print(f"Predicto chat failed: {e}")
    return False

def test_predicto_stock_analysis():
    """Test Predicto stock analysis"""
    try:
        response = requests.post(
            "http://localhost:8080/api/v1/predicto/analyze",
            json={"symbol": "AAPL"},
            timeout=10
        )
        
        print(f"Predicto stock analysis: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Symbol: {data.get('symbol', 'unknown')}")
            print(f"Powered by: {data.get('powered_by', 'unknown')}")
            return True
    except Exception as e:
        print(f"Predicto stock analysis failed: {e}")
    return False

def main():
    """Run all tests"""
    print("🔮 Testing Predicto API Endpoints")
    print("=" * 40)
    
    # Wait a moment for server to be ready
    time.sleep(2)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Predicto Capabilities", test_predicto_capabilities),
        ("Predicto Chat", test_predicto_chat),
        ("Predicto Stock Analysis", test_predicto_stock_analysis)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            print(f"✅ {test_name} PASSED")
            passed += 1
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n{'='*40}")
    print(f"Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All API tests passed! Predicto is working!")
    else:
        print("⚠️ Some tests failed. Check the server logs.")

if __name__ == "__main__":
    main()
