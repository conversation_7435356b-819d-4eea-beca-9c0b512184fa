"""
Production Readiness Test for Predicto-Integrated A.T.L.A.S
Tests error handling, graceful degradation, and production features
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8080"

def test_error_handling():
    """Test error handling and graceful degradation"""
    print("\n🛡️ Testing Error Handling & Graceful Degradation")
    
    tests_passed = 0
    total_tests = 0
    
    # Test invalid JSON
    total_tests += 1
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            data="invalid json",
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        if response.status_code == 422:  # Unprocessable Entity
            print("  ✅ Invalid JSON handling: Proper error response")
            tests_passed += 1
        else:
            print(f"  ❌ Invalid JSON handling: Unexpected status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Invalid JSON test failed: {e}")
    
    # Test missing required fields
    total_tests += 1
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"session_id": "test"},  # Missing message
            timeout=5
        )
        if response.status_code == 422:
            print("  ✅ Missing fields handling: Proper validation")
            tests_passed += 1
        else:
            print(f"  ❌ Missing fields handling: Unexpected status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Missing fields test failed: {e}")
    
    # Test very long message
    total_tests += 1
    try:
        long_message = "A" * 10000  # Very long message
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": long_message, "session_id": "test_long"},
            timeout=10
        )
        if response.status_code == 200:
            print("  ✅ Long message handling: Graceful processing")
            tests_passed += 1
        else:
            print(f"  ❌ Long message handling: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Long message test failed: {e}")
    
    # Test invalid endpoint
    total_tests += 1
    try:
        response = requests.get(f"{BASE_URL}/api/v1/nonexistent", timeout=5)
        if response.status_code == 404:
            print("  ✅ Invalid endpoint handling: Proper 404 response")
            tests_passed += 1
        else:
            print(f"  ❌ Invalid endpoint handling: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Invalid endpoint test failed: {e}")
    
    return tests_passed, total_tests

def test_endpoint_responses():
    """Test all endpoint responses"""
    print("\n🌐 Testing Endpoint Responses")
    
    tests_passed = 0
    total_tests = 0
    
    endpoints = [
        ("GET", "/api/v1/health", None),
        ("GET", "/api/v1/predicto/capabilities", None),
        ("POST", "/api/v1/chat", {"message": "Test", "session_id": "endpoint_test"}),
        ("POST", "/api/v1/predicto/analyze", {"symbol": "AAPL"}),
        ("GET", "/", None),  # Web interface
    ]
    
    for method, endpoint, data in endpoints:
        total_tests += 1
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            else:
                response = requests.post(f"{BASE_URL}{endpoint}", json=data, timeout=10)
            
            if response.status_code == 200:
                print(f"  ✅ {method} {endpoint}: Working (200)")
                tests_passed += 1
            else:
                print(f"  ❌ {method} {endpoint}: Status {response.status_code}")
        except Exception as e:
            print(f"  ❌ {method} {endpoint}: Error - {e}")
    
    return tests_passed, total_tests

def test_predicto_primary_interface():
    """Test that Predicto serves as the primary interface"""
    print("\n🔮 Testing Predicto as Primary Interface")
    
    tests_passed = 0
    total_tests = 0
    
    # Test capabilities endpoint shows Predicto prominently
    total_tests += 1
    try:
        response = requests.get(f"{BASE_URL}/api/v1/predicto/capabilities", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if "predicto_status" in data and "natural_language_access" in data:
                print("  ✅ Predicto capabilities: Prominently featured")
                tests_passed += 1
            else:
                print("  ❌ Predicto capabilities: Missing key features")
        else:
            print(f"  ❌ Predicto capabilities: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Predicto capabilities test failed: {e}")
    
    # Test chat endpoint uses Predicto
    total_tests += 1
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": "What can you do?", "session_id": "primary_test"},
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            powered_by = data.get("context", {}).get("powered_by", "")
            if "Predicto" in powered_by:
                print("  ✅ Chat endpoint: Powered by Predicto")
                tests_passed += 1
            else:
                print(f"  ❌ Chat endpoint: Powered by {powered_by}")
        else:
            print(f"  ❌ Chat endpoint: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Chat endpoint test failed: {e}")
    
    # Test web interface accessibility
    total_tests += 1
    try:
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            content = response.text
            if "Predicto" in content:
                print("  ✅ Web interface: Features Predicto prominently")
                tests_passed += 1
            else:
                print("  ❌ Web interface: Predicto not prominently featured")
        else:
            print(f"  ❌ Web interface: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Web interface test failed: {e}")
    
    return tests_passed, total_tests

def test_backward_compatibility():
    """Test backward compatibility with existing functionality"""
    print("\n🔄 Testing Backward Compatibility")
    
    tests_passed = 0
    total_tests = 0
    
    # Test health endpoint (existing functionality)
    total_tests += 1
    try:
        response = requests.get(f"{BASE_URL}/api/v1/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "status" in data:
                print("  ✅ Health endpoint: Backward compatible")
                tests_passed += 1
            else:
                print("  ❌ Health endpoint: Missing expected fields")
        else:
            print(f"  ❌ Health endpoint: Status {response.status_code}")
    except Exception as e:
        print(f"  ❌ Health endpoint test failed: {e}")
    
    # Test that existing endpoints still work
    total_tests += 1
    try:
        # Test if we can still access system without going through Predicto
        response = requests.get(f"{BASE_URL}/api/v1/health", timeout=5)
        if response.status_code == 200:
            print("  ✅ Direct API access: Still available")
            tests_passed += 1
        else:
            print("  ❌ Direct API access: Broken")
    except Exception as e:
        print(f"  ❌ Direct API access test failed: {e}")
    
    return tests_passed, total_tests

def test_performance_and_reliability():
    """Test performance and reliability"""
    print("\n⚡ Testing Performance & Reliability")
    
    tests_passed = 0
    total_tests = 0
    
    # Test response times
    total_tests += 1
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": "Quick test", "session_id": "perf_test"},
            timeout=10
        )
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200 and response_time < 5.0:
            print(f"  ✅ Response time: {response_time:.2f}s (< 5s)")
            tests_passed += 1
        else:
            print(f"  ❌ Response time: {response_time:.2f}s (too slow or failed)")
    except Exception as e:
        print(f"  ❌ Response time test failed: {e}")
    
    # Test concurrent requests
    total_tests += 1
    try:
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request(session_id):
            try:
                response = requests.post(
                    f"{BASE_URL}/api/v1/chat",
                    json={"message": f"Concurrent test {session_id}", "session_id": f"concurrent_{session_id}"},
                    timeout=10
                )
                results.put(response.status_code == 200)
            except:
                results.put(False)
        
        # Start 3 concurrent requests
        threads = []
        for i in range(3):
            thread = threading.Thread(target=make_request, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all to complete
        for thread in threads:
            thread.join()
        
        # Check results
        success_count = 0
        while not results.empty():
            if results.get():
                success_count += 1
        
        if success_count >= 2:  # At least 2 out of 3 should succeed
            print(f"  ✅ Concurrent requests: {success_count}/3 succeeded")
            tests_passed += 1
        else:
            print(f"  ❌ Concurrent requests: Only {success_count}/3 succeeded")
    except Exception as e:
        print(f"  ❌ Concurrent requests test failed: {e}")
    
    return tests_passed, total_tests

def main():
    """Run all production readiness tests"""
    print("🔮 PREDICTO PRODUCTION READINESS TEST")
    print("=" * 60)
    print(f"Test Time: {datetime.now()}")
    print(f"Server: {BASE_URL}")
    print("=" * 60)
    
    all_tests_passed = 0
    all_total_tests = 0
    
    # Run all test suites
    test_suites = [
        ("Error Handling", test_error_handling),
        ("Endpoint Responses", test_endpoint_responses),
        ("Predicto Primary Interface", test_predicto_primary_interface),
        ("Backward Compatibility", test_backward_compatibility),
        ("Performance & Reliability", test_performance_and_reliability)
    ]
    
    for suite_name, test_func in test_suites:
        print(f"\n{'='*20} {suite_name} {'='*20}")
        passed, total = test_func()
        all_tests_passed += passed
        all_total_tests += total
        print(f"Suite Results: {passed}/{total} tests passed")
    
    # Final results
    print("\n" + "=" * 60)
    print("🔮 PRODUCTION READINESS RESULTS")
    print("=" * 60)
    
    success_rate = (all_tests_passed / all_total_tests) * 100 if all_total_tests > 0 else 0
    print(f"Overall: {all_tests_passed}/{all_total_tests} tests passed ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("\n🎉 EXCELLENT! System is production ready!")
        print("   ✅ Error handling works correctly")
        print("   ✅ All endpoints responding properly")
        print("   ✅ Predicto serves as primary interface")
        print("   ✅ Backward compatibility maintained")
        print("   ✅ Performance meets requirements")
        print("\n🚀 Ready for production deployment!")
    elif success_rate >= 75:
        print("\n✅ GOOD! System is mostly production ready")
        print("   Some minor issues may need attention")
    else:
        print("\n⚠️ NEEDS WORK! Several issues detected")
        print("   Please review failed tests before production")
    
    print("=" * 60)
    
    return success_rate >= 90

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
