"""
Predicto Server Startup Script
Starts the A.T.L.A.S server with Predicto integration in validation mode for testing
"""

import os
import sys
import asyncio
import logging
from datetime import datetime

# Set validation mode before importing any modules
os.environ["VALIDATION_MODE"] = "true"

print("🔮 Starting A.T.L.A.S with Predicto Integration")
print("=" * 60)
print(f"Startup time: {datetime.now()}")
print(f"Validation mode: {os.environ.get('VALIDATION_MODE', 'false')}")
print("=" * 60)

try:
    # Test imports first
    print("\n📦 Testing module imports...")
    
    print("  Importing config...")
    from config import settings, LOGGING_CONFIG
    print("  ✅ Config imported successfully")
    
    print("  Importing models...")
    from models import AIResponse, EngineStatus
    print("  ✅ Models imported successfully")
    
    print("  Importing Predicto components...")
    from atlas_predicto_engine import PredictoConversationalEngine
    print("  ✅ Predicto Engine imported")
    
    from atlas_stock_intelligence_hub import StockIntelligenceHub
    print("  ✅ Stock Intelligence Hub imported")
    
    from atlas_unified_access_layer import UnifiedSystemAccessLayer
    print("  ✅ Unified Access Layer imported")
    
    from atlas_conversation_flow_manager import ConversationFlowManager
    print("  ✅ Conversation Flow Manager imported")
    
    print("  Importing core A.T.L.A.S components...")
    from atlas_ai_engine import AtlasAIEngine
    print("  ✅ AI Engine imported")
    
    from atlas_orchestrator import AtlasOrchestrator
    print("  ✅ Orchestrator imported")
    
    print("\n🎉 All imports successful!")
    
except ImportError as e:
    print(f"\n❌ Import error: {e}")
    print("Please check that all required modules are available.")
    sys.exit(1)

except Exception as e:
    print(f"\n❌ Unexpected error during import: {e}")
    sys.exit(1)

# Now start the server
try:
    print("\n🚀 Starting A.T.L.A.S server with Predicto...")
    
    # Import and run the server
    import atlas_server
    
    print("✅ Server module imported successfully")
    print("\n🔮 Predicto-powered A.T.L.A.S is starting...")
    print("   Access the web interface at: http://localhost:8000")
    print("   API documentation at: http://localhost:8000/docs")
    print("   Predicto chat endpoint: http://localhost:8000/api/v1/chat")
    print("\n💡 Try these Predicto conversations:")
    print("   • 'Hello Predicto, what can you do?'")
    print("   • 'Analyze AAPL stock'")
    print("   • 'Scan for market opportunities'")
    print("   • 'Explain technical analysis'")
    print("\n" + "=" * 60)
    
    # The server will start when this script is run
    
except Exception as e:
    print(f"\n❌ Server startup error: {e}")
    print("Please check the error details above.")
    sys.exit(1)
