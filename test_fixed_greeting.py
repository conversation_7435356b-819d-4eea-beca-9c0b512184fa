"""
Test the fixed greeting and branding responses
"""

import requests
import json

BASE_URL = "http://localhost:8080"

def test_greeting_response():
    """Test the improved greeting response"""
    print("🔮 Testing Fixed Greeting Response")
    print("=" * 40)
    
    greetings = ["hello", "hi", "Hello Predicto", "good morning"]
    
    for greeting in greetings:
        print(f"\nTesting: '{greeting}'")
        try:
            response = requests.post(
                f"{BASE_URL}/api/v1/chat",
                json={"message": greeting, "session_id": "greeting_test"},
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Status: {response.status_code}")
                print(f"Response Type: {data.get('type', 'unknown')}")
                print(f"Confidence: {data.get('confidence', 'unknown')}")
                
                # Check branding in context
                context = data.get('context', {})
                powered_by = context.get('powered_by', 'Not found')
                system = context.get('system', 'Not found')
                
                print(f"Powered By: {powered_by}")
                print(f"System: {system}")
                
                # Check if response mentions correct branding
                response_text = data.get('response', '')
                if 'A.T.L.A.S' in response_text and 'Predicto' in response_text:
                    print("✅ Correct branding in response")
                else:
                    print("⚠️ Check branding in response")
                
                # Check if it's not the old initialization message
                if 'AI systems are currently initializing' in response_text:
                    print("❌ Still showing initialization message!")
                else:
                    print("✅ No false initialization message")
                
                print(f"Response Preview: {response_text[:150]}...")
                
            else:
                print(f"❌ Status: {response.status_code}")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("-" * 40)

def test_non_greeting():
    """Test non-greeting message to ensure normal processing"""
    print("\n🔍 Testing Non-Greeting Message")
    print("=" * 40)
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json={"message": "Analyze AAPL stock", "session_id": "analysis_test"},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"Response Type: {data.get('type', 'unknown')}")
            
            # Check branding
            context = data.get('context', {})
            powered_by = context.get('powered_by', 'Not found')
            print(f"Powered By: {powered_by}")
            
            response_text = data.get('response', '')
            print(f"Response Preview: {response_text[:150]}...")
            
        else:
            print(f"❌ Status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run greeting tests"""
    print("🔮 TESTING FIXED PREDICTO GREETING & BRANDING")
    print("=" * 60)
    
    # Test greetings
    test_greeting_response()
    
    # Test non-greeting
    test_non_greeting()
    
    print("\n" + "=" * 60)
    print("🔮 GREETING TESTS COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
