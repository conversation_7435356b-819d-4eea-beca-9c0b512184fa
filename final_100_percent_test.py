"""
Final 100% Functionality Test
Comprehensive test to verify all Predicto features are working
"""

import requests
import json
import time

BASE_URL = "http://localhost:8080"

def test_endpoint(name, method, url, data=None, expected_status=200):
    """Test a single endpoint"""
    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        else:
            response = requests.post(url, json=data, timeout=15)
        
        if response.status_code == expected_status:
            print(f"✅ {name}: SUCCESS ({response.status_code})")
            return True
        else:
            print(f"❌ {name}: FAILED ({response.status_code})")
            return False
    except Exception as e:
        print(f"❌ {name}: ERROR - {e}")
        return False

def main():
    """Run final comprehensive test"""
    print("🔮 FINAL 100% FUNCTIONALITY TEST")
    print("=" * 50)
    
    tests = [
        # Core endpoints
        ("Health Check", "GET", f"{BASE_URL}/api/v1/health"),
        ("Predicto Capabilities", "GET", f"{BASE_URL}/api/v1/predicto/capabilities"),
        ("Web Interface", "GET", f"{BASE_URL}/"),
        
        # Chat functionality
        ("Predicto Chat - Hello", "POST", f"{BASE_URL}/api/v1/chat", 
         {"message": "Hello Predicto", "session_id": "final_test"}),
        ("Predicto Chat - Stock Analysis", "POST", f"{BASE_URL}/api/v1/chat", 
         {"message": "Analyze AAPL", "session_id": "final_test"}),
        ("Predicto Chat - Market Scan", "POST", f"{BASE_URL}/api/v1/chat", 
         {"message": "Scan for opportunities", "session_id": "final_test"}),
        
        # Stock analysis endpoint (previously failing)
        ("Stock Analysis Endpoint", "POST", f"{BASE_URL}/api/v1/predicto/analyze", 
         {"symbol": "AAPL"}),
        
        # Error handling tests
        ("Invalid JSON Handling", "POST", f"{BASE_URL}/api/v1/chat", 
         {"session_id": "test"}, 422),  # Missing message field
        ("Invalid Endpoint", "GET", f"{BASE_URL}/api/v1/nonexistent", None, 404),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, method, url, data, *expected in tests:
        expected_status = expected[0] if expected else 200
        if test_endpoint(test_name, method, url, data, expected_status):
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    success_rate = (passed / total) * 100
    
    print("\n" + "=" * 50)
    print("🔮 FINAL TEST RESULTS")
    print("=" * 50)
    print(f"Tests Passed: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("\n🎉 100% SUCCESS! PRODUCTION READY!")
        print("✅ All endpoints working perfectly")
        print("✅ Predicto chat functionality working")
        print("✅ Stock analysis endpoint fixed")
        print("✅ Error handling working correctly")
        print("✅ System is fully functional")
        print("\n🚀 Predicto integration is COMPLETE!")
        return True
    else:
        print(f"\n⚠️ {total - passed} tests failed")
        print("System needs additional fixes")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
