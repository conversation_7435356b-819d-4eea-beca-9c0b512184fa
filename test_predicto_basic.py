"""
Basic Predicto Integration Test
Tests core functionality without requiring API keys
"""

import os
import asyncio
import sys
from datetime import datetime

# Set validation mode before importing
os.environ["VALIDATION_MODE"] = "true"

try:
    from atlas_predicto_engine import PredictoConversationalEngine
    from atlas_stock_intelligence_hub import StockI<PERSON>lligenceH<PERSON>
    from atlas_unified_access_layer import UnifiedSystemAccessLayer
    from atlas_conversation_flow_manager import ConversationFlowManager
    from atlas_ai_engine import AtlasAIEngine
    from models import AIResponse, EngineStatus
    print("✅ All Predicto modules imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


class MockOrchestrator:
    """Mock orchestrator for testing"""
    def __init__(self):
        self.market_engine = MockMarketEngine()
        self.sentiment_analyzer = MockSentimentAnalyzer()
        self.ml_predictor = MockMLPredictor()

class MockMarketEngine:
    async def get_quote(self, symbol):
        return {"symbol": symbol, "price": 150.0, "change_percent": 2.5}

    async def scan_ttm_squeeze(self, symbols):
        return [{"symbol": symbol, "strength": 4} for symbol in symbols]

    async def get_predicto_forecast(self, symbol, days=5):
        return {"symbol": symbol, "predicted_price": 155.0, "confidence": 0.8}

    async def scan_market(self, strength="moderate"):
        return [
            {"symbol": "AAPL", "strength": 5, "signal_type": "ttm_squeeze"},
            {"symbol": "TSLA", "strength": 4, "signal_type": "ttm_squeeze"}
        ]

class MockSentimentAnalyzer:
    async def analyze_symbol_sentiment(self, symbol):
        return {"symbol": symbol, "overall_sentiment": "bullish", "sentiment_score": 0.7}

class MockMLPredictor:
    async def predict_returns(self, symbol, timeframe="5min"):
        return {"symbol": symbol, "predicted_return": 0.03, "confidence": 0.75}


async def test_predicto_engine():
    """Test Predicto conversational engine"""
    print("\n🔮 Testing Predicto Conversational Engine...")
    
    try:
        engine = PredictoConversationalEngine()
        await engine.initialize()
        
        if engine.status == EngineStatus.ACTIVE or engine.status == EngineStatus.INACTIVE:
            print("  ✅ Predicto Engine initialized")
            
            # Test basic conversation
            mock_orchestrator = MockOrchestrator()
            response = await engine.process_conversation(
                "Hello Predicto, analyze AAPL", "test_session", mock_orchestrator
            )
            
            if isinstance(response, AIResponse) and response.response:
                print(f"  ✅ Conversation processing works")
                print(f"     Response: {response.response[:100]}...")
            else:
                print("  ❌ Conversation processing failed")
                
        else:
            print("  ❌ Predicto Engine failed to initialize")
            
        await engine.cleanup()
        
    except Exception as e:
        print(f"  ❌ Predicto Engine test failed: {e}")


async def test_stock_intelligence_hub():
    """Test Stock Intelligence Hub"""
    print("\n🧠 Testing Stock Intelligence Hub...")
    
    try:
        hub = StockIntelligenceHub()
        await hub.initialize()
        
        if hub.status == EngineStatus.ACTIVE:
            print("  ✅ Stock Intelligence Hub initialized")
            
            # Test stock analysis
            mock_orchestrator = MockOrchestrator()
            analysis = await hub.analyze_stock_comprehensive("AAPL", mock_orchestrator)
            
            if analysis and "symbol" in analysis:
                print("  ✅ Stock analysis works")
                print(f"     Analysis for: {analysis['symbol']}")
            else:
                print("  ❌ Stock analysis failed")
                
        else:
            print("  ❌ Stock Intelligence Hub failed to initialize")
            
        await hub.cleanup()
        
    except Exception as e:
        print(f"  ❌ Stock Intelligence Hub test failed: {e}")


async def test_unified_access_layer():
    """Test Unified Access Layer"""
    print("\n🌐 Testing Unified Access Layer...")
    
    try:
        layer = UnifiedSystemAccessLayer()
        await layer.initialize()
        
        if layer.status == EngineStatus.ACTIVE:
            print("  ✅ Unified Access Layer initialized")
            
            # Test capability routing
            mock_orchestrator = MockOrchestrator()
            result = await layer.route_natural_language_request(
                "Analyze AAPL stock", mock_orchestrator
            )
            
            if result and "capabilities_used" in result:
                print("  ✅ Natural language routing works")
                print(f"     Capabilities used: {result['capabilities_used']}")
            else:
                print("  ❌ Natural language routing failed")
                
            # Test available capabilities
            capabilities = layer.get_available_capabilities()
            if capabilities and capabilities["total_capabilities"] >= 25:
                print(f"  ✅ {capabilities['total_capabilities']} capabilities available")
            else:
                print("  ❌ Insufficient capabilities available")
                
        else:
            print("  ❌ Unified Access Layer failed to initialize")
            
        await layer.cleanup()
        
    except Exception as e:
        print(f"  ❌ Unified Access Layer test failed: {e}")


async def test_conversation_flow_manager():
    """Test Conversation Flow Manager"""
    print("\n💬 Testing Conversation Flow Manager...")
    
    try:
        manager = ConversationFlowManager()
        await manager.initialize()
        
        if manager.status == EngineStatus.ACTIVE:
            print("  ✅ Conversation Flow Manager initialized")
            
            # Test conversation flow
            mock_response = AIResponse(response="Test response", type="stock_analysis", confidence=0.8)
            flow_data = await manager.process_conversation_turn(
                "Analyze AAPL", "test_session", mock_response
            )
            
            if flow_data and "conversation_context" in flow_data:
                print("  ✅ Conversation flow processing works")
                print(f"     Flow data keys: {list(flow_data.keys())}")
            else:
                print("  ❌ Conversation flow processing failed")
                
        else:
            print("  ❌ Conversation Flow Manager failed to initialize")
            
        await manager.cleanup()
        
    except Exception as e:
        print(f"  ❌ Conversation Flow Manager test failed: {e}")


async def test_ai_engine_integration():
    """Test full AI Engine integration"""
    print("\n🤖 Testing AI Engine Integration...")
    
    try:
        ai_engine = AtlasAIEngine()
        await ai_engine.initialize()
        
        # Check Predicto components
        components_status = {
            "Predicto Engine": ai_engine.predicto_engine is not None,
            "Stock Intelligence Hub": ai_engine.stock_intelligence_hub is not None,
            "Unified Access Layer": ai_engine.unified_access_layer is not None,
            "Conversation Flow Manager": ai_engine.conversation_flow_manager is not None
        }
        
        for component, status in components_status.items():
            if status:
                print(f"  ✅ {component}: Integrated")
            else:
                print(f"  ❌ {component}: Missing")
        
        # Test message processing
        mock_orchestrator = MockOrchestrator()
        response = await ai_engine.process_message(
            "Hello Predicto, what can you do?", "integration_test", mock_orchestrator
        )
        
        if isinstance(response, AIResponse) and response.response:
            print("  ✅ Message processing through Predicto works")
            print(f"     Response type: {response.type}")
        else:
            print("  ❌ Message processing failed")
            
        await ai_engine.cleanup()
        
    except Exception as e:
        print(f"  ❌ AI Engine integration test failed: {e}")


async def test_conversation_scenarios():
    """Test various conversation scenarios"""
    print("\n🗣️  Testing Conversation Scenarios...")
    
    scenarios = [
        ("Stock Analysis", "Analyze Apple stock for me"),
        ("Market Scanning", "Scan for trading opportunities"),
        ("Sentiment Analysis", "What's the sentiment on Tesla?"),
        ("Education", "Explain what technical analysis means"),
        ("Options Trading", "Tell me about options strategies"),
        ("Portfolio Management", "How should I optimize my portfolio?"),
        ("Risk Management", "What are the risks of trading NVDA?")
    ]
    
    try:
        ai_engine = AtlasAIEngine()
        await ai_engine.initialize()
        mock_orchestrator = MockOrchestrator()
        
        successful_scenarios = 0
        
        for scenario_name, message in scenarios:
            try:
                response = await ai_engine.process_message(message, f"scenario_{scenario_name.lower()}", mock_orchestrator)
                
                if isinstance(response, AIResponse) and response.response and len(response.response) > 10:
                    print(f"  ✅ {scenario_name}: Working")
                    successful_scenarios += 1
                else:
                    print(f"  ❌ {scenario_name}: Failed")
                    
            except Exception as e:
                print(f"  ❌ {scenario_name}: Error - {str(e)}")
        
        success_rate = (successful_scenarios / len(scenarios)) * 100
        print(f"\n  📊 Scenario Success Rate: {successful_scenarios}/{len(scenarios)} ({success_rate:.1f}%)")
        
        await ai_engine.cleanup()
        
    except Exception as e:
        print(f"  ❌ Conversation scenarios test failed: {e}")


async def main():
    """Run all basic tests"""
    print("🔮 PREDICTO INTEGRATION - BASIC FUNCTIONALITY TEST")
    print("=" * 60)
    print(f"Test started at: {datetime.now()}")
    print(f"Validation mode: {os.environ.get('VALIDATION_MODE', 'false')}")
    
    # Run all tests
    await test_predicto_engine()
    await test_stock_intelligence_hub()
    await test_unified_access_layer()
    await test_conversation_flow_manager()
    await test_ai_engine_integration()
    await test_conversation_scenarios()
    
    print("\n" + "=" * 60)
    print("🔮 BASIC FUNCTIONALITY TEST COMPLETED")
    print("=" * 60)
    print("\n🎉 If all tests passed, Predicto is successfully integrated!")
    print("   Users can now access A.T.L.A.S capabilities through natural conversation.")
    print("\n💡 Next steps:")
    print("   1. Start the A.T.L.A.S server: python atlas_server.py")
    print("   2. Open the web interface: atlas_interface.html")
    print("   3. Try conversing with Predicto!")


if __name__ == "__main__":
    asyncio.run(main())
