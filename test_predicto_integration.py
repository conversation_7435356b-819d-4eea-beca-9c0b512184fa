"""
Comprehensive Test Suite for Predicto Integration
Tests all aspects of the Predicto conversational AI interface and system integration
"""

import asyncio
import pytest
import json
from datetime import datetime
from typing import Dict, Any, List

# Test imports
from atlas_predicto_engine import PredictoConversationalEngine
from atlas_stock_intelligence_hub import StockI<PERSON>lligenceH<PERSON>
from atlas_unified_access_layer import UnifiedSystemAccessLayer
from atlas_conversation_flow_manager import ConversationFlowManager
from atlas_ai_engine import AtlasAIEngine
from atlas_orchestrator import AtlasOrchestrator
from models import AIResponse, EngineStatus


class TestPredictoIntegration:
    """Comprehensive test suite for Predicto integration"""

    @pytest.fixture
    async def predicto_engine(self):
        """Create and initialize Predicto engine for testing"""
        engine = PredictoConversationalEngine()
        await engine.initialize()
        yield engine
        await engine.cleanup()

    @pytest.fixture
    async def stock_intelligence_hub(self):
        """Create and initialize Stock Intelligence Hub for testing"""
        hub = StockIntelligenceHub()
        await hub.initialize()
        yield hub
        await hub.cleanup()

    @pytest.fixture
    async def unified_access_layer(self):
        """Create and initialize Unified Access Layer for testing"""
        layer = UnifiedSystemAccessLayer()
        await layer.initialize()
        yield layer
        await layer.cleanup()

    @pytest.fixture
    async def conversation_flow_manager(self):
        """Create and initialize Conversation Flow Manager for testing"""
        manager = ConversationFlowManager()
        await manager.initialize()
        yield manager
        await manager.cleanup()

    @pytest.fixture
    async def mock_orchestrator(self):
        """Create a mock orchestrator for testing"""
        class MockOrchestrator:
            def __init__(self):
                self.market_engine = MockMarketEngine()
                self.sentiment_analyzer = MockSentimentAnalyzer()
                self.ml_predictor = MockMLPredictor()

        class MockMarketEngine:
            async def get_quote(self, symbol):
                return {"symbol": symbol, "price": 150.0, "change_percent": 2.5}

            async def scan_ttm_squeeze(self, symbols):
                return [{"symbol": symbol, "strength": 4} for symbol in symbols]

            async def get_predicto_forecast(self, symbol, days=5):
                return {"symbol": symbol, "predicted_price": 155.0, "confidence": 0.8}

        class MockSentimentAnalyzer:
            async def analyze_symbol_sentiment(self, symbol):
                return {"symbol": symbol, "overall_sentiment": "bullish", "sentiment_score": 0.7}

        class MockMLPredictor:
            async def predict_returns(self, symbol, timeframe="5min"):
                return {"symbol": symbol, "predicted_return": 0.03, "confidence": 0.75}

        return MockOrchestrator()

    # Core Engine Tests
    async def test_predicto_engine_initialization(self, predicto_engine):
        """Test Predicto engine initialization"""
        assert predicto_engine.status == EngineStatus.ACTIVE
        assert predicto_engine.system_prompt is not None
        assert len(predicto_engine.capability_map) > 0

    async def test_predicto_conversation_processing(self, predicto_engine, mock_orchestrator):
        """Test basic conversation processing"""
        response = await predicto_engine.process_conversation(
            "Analyze AAPL stock", "test_session", mock_orchestrator
        )
        
        assert isinstance(response, AIResponse)
        assert response.response is not None
        assert response.type in ["stock_analysis", "system_capability", "general_conversation"]

    async def test_stock_analysis_conversation(self, predicto_engine, mock_orchestrator):
        """Test stock analysis conversation flow"""
        test_messages = [
            "What's the outlook for AAPL?",
            "Analyze Tesla stock",
            "How is NVDA performing?"
        ]
        
        for message in test_messages:
            response = await predicto_engine.process_conversation(
                message, "test_session", mock_orchestrator
            )
            assert response.response is not None
            assert "error" not in response.response.lower()

    # Stock Intelligence Hub Tests
    async def test_stock_intelligence_hub_initialization(self, stock_intelligence_hub):
        """Test Stock Intelligence Hub initialization"""
        assert stock_intelligence_hub.status == EngineStatus.ACTIVE
        assert stock_intelligence_hub.technical_analyzer is not None
        assert stock_intelligence_hub.sentiment_analyzer is not None

    async def test_comprehensive_stock_analysis(self, stock_intelligence_hub, mock_orchestrator):
        """Test comprehensive stock analysis"""
        analysis = await stock_intelligence_hub.analyze_stock_comprehensive("AAPL", mock_orchestrator)
        
        assert analysis["symbol"] == "AAPL"
        assert "technical_analysis" in analysis
        assert "sentiment_analysis" in analysis
        assert "prediction_analysis" in analysis
        assert "overall_assessment" in analysis

    async def test_stock_comparison(self, stock_intelligence_hub, mock_orchestrator):
        """Test stock comparison functionality"""
        comparison = await stock_intelligence_hub.compare_stocks(["AAPL", "TSLA"], mock_orchestrator)
        
        assert "symbols" in comparison
        assert "individual_analyses" in comparison
        assert "comparison_insights" in comparison

    # Unified Access Layer Tests
    async def test_unified_access_layer_initialization(self, unified_access_layer):
        """Test Unified Access Layer initialization"""
        assert unified_access_layer.status == EngineStatus.ACTIVE
        assert len(unified_access_layer.feature_registry) >= 25
        assert len(unified_access_layer.capability_map) > 0

    async def test_natural_language_routing(self, unified_access_layer, mock_orchestrator):
        """Test natural language request routing"""
        test_requests = [
            "Analyze AAPL",
            "Scan for opportunities",
            "What's the sentiment on Tesla?",
            "Predict NVDA price movement",
            "Explain options trading"
        ]
        
        for request in test_requests:
            result = await unified_access_layer.route_natural_language_request(request, mock_orchestrator)
            assert "capabilities_used" in result
            assert "results" in result
            assert len(result["capabilities_used"]) > 0

    async def test_capability_execution(self, unified_access_layer, mock_orchestrator):
        """Test individual capability execution"""
        capabilities = ["sentiment_analysis", "lstm_prediction", "ttm_squeeze_detection"]
        parameters = {"symbol": "AAPL"}
        
        for capability in capabilities:
            if capability in unified_access_layer.feature_registry:
                feature_info = unified_access_layer.feature_registry[capability]
                result = await unified_access_layer._execute_single_capability(
                    capability, feature_info, parameters, mock_orchestrator
                )
                assert result is not None

    # Conversation Flow Manager Tests
    async def test_conversation_flow_initialization(self, conversation_flow_manager):
        """Test Conversation Flow Manager initialization"""
        assert conversation_flow_manager.status == EngineStatus.ACTIVE
        assert len(conversation_flow_manager.conversation_templates) > 0
        assert len(conversation_flow_manager.transition_rules) > 0

    async def test_conversation_state_management(self, conversation_flow_manager):
        """Test conversation state management"""
        mock_response = AIResponse(response="Test response", type="stock_analysis", confidence=0.8)
        
        flow_data = await conversation_flow_manager.process_conversation_turn(
            "Analyze AAPL", "test_session", mock_response
        )
        
        assert "conversation_context" in flow_data
        assert "flow_enhancements" in flow_data
        assert "suggestions" in flow_data

    async def test_conversation_transitions(self, conversation_flow_manager):
        """Test conversation state transitions"""
        session_id = "test_transition_session"
        mock_response = AIResponse(response="Analysis complete", type="stock_analysis", confidence=0.8)
        
        # First message - stock analysis
        await conversation_flow_manager.process_conversation_turn(
            "Analyze AAPL", session_id, mock_response
        )
        
        # Second message - transition to scanning
        flow_data = await conversation_flow_manager.process_conversation_turn(
            "Now scan for similar opportunities", session_id, mock_response
        )
        
        context = flow_data["conversation_context"]
        assert len(context.previous_states) > 0

    # Integration Tests
    async def test_full_ai_engine_integration(self):
        """Test full AI engine integration with Predicto"""
        ai_engine = AtlasAIEngine()
        ai_engine.validation_mode = True  # Use validation mode for testing
        
        await ai_engine.initialize()
        
        # Test that Predicto components are initialized
        assert ai_engine.predicto_engine is not None
        assert ai_engine.stock_intelligence_hub is not None
        assert ai_engine.unified_access_layer is not None
        assert ai_engine.conversation_flow_manager is not None
        
        await ai_engine.cleanup()

    async def test_end_to_end_conversation_flow(self, mock_orchestrator):
        """Test end-to-end conversation flow"""
        # Initialize full AI engine
        ai_engine = AtlasAIEngine()
        ai_engine.validation_mode = True
        await ai_engine.initialize()
        
        try:
            # Test conversation flow
            test_conversations = [
                "Hello, I'm new to trading",
                "Can you analyze Apple stock?",
                "What about Tesla?",
                "Scan for market opportunities",
                "Explain what TTM Squeeze means"
            ]
            
            session_id = "test_e2e_session"
            
            for message in test_conversations:
                response = await ai_engine.process_message(message, session_id, mock_orchestrator)
                assert isinstance(response, AIResponse)
                assert response.response is not None
                assert len(response.response) > 0
                
        finally:
            await ai_engine.cleanup()

    # Performance Tests
    async def test_response_time_performance(self, predicto_engine, mock_orchestrator):
        """Test response time performance"""
        start_time = datetime.now()
        
        response = await predicto_engine.process_conversation(
            "Quick analysis of AAPL", "perf_test", mock_orchestrator
        )
        
        end_time = datetime.now()
        response_time = (end_time - start_time).total_seconds()
        
        assert response_time < 5.0  # Should respond within 5 seconds
        assert response.response is not None

    async def test_concurrent_conversations(self, predicto_engine, mock_orchestrator):
        """Test handling multiple concurrent conversations"""
        async def single_conversation(session_id: str):
            return await predicto_engine.process_conversation(
                f"Analyze AAPL for session {session_id}", session_id, mock_orchestrator
            )
        
        # Run 5 concurrent conversations
        tasks = [single_conversation(f"session_{i}") for i in range(5)]
        responses = await asyncio.gather(*tasks)
        
        assert len(responses) == 5
        for response in responses:
            assert isinstance(response, AIResponse)
            assert response.response is not None

    # Error Handling Tests
    async def test_error_handling_invalid_symbol(self, predicto_engine, mock_orchestrator):
        """Test error handling for invalid symbols"""
        response = await predicto_engine.process_conversation(
            "Analyze INVALIDSTOCK", "error_test", mock_orchestrator
        )
        
        # Should handle gracefully without crashing
        assert isinstance(response, AIResponse)
        assert response.response is not None

    async def test_error_handling_empty_message(self, predicto_engine, mock_orchestrator):
        """Test error handling for empty messages"""
        response = await predicto_engine.process_conversation(
            "", "error_test", mock_orchestrator
        )
        
        assert isinstance(response, AIResponse)
        assert response.response is not None

    # Feature Coverage Tests
    async def test_all_capability_keywords(self, unified_access_layer):
        """Test that all capability keywords are properly mapped"""
        capabilities = unified_access_layer.get_available_capabilities()
        
        assert capabilities["total_capabilities"] >= 25
        assert len(capabilities["natural_language_keywords"]) > 0
        
        # Test specific capability categories
        required_categories = [
            "sentiment_analysis", "lstm_prediction", "ttm_squeeze_detection",
            "options_analysis", "portfolio_optimization", "trading_education"
        ]
        
        for category in required_categories:
            assert category in capabilities["capabilities"]

    async def test_conversation_memory(self, conversation_flow_manager):
        """Test conversation memory and context retention"""
        session_id = "memory_test"
        mock_response = AIResponse(response="Test", type="stock_analysis", confidence=0.8)
        
        # First conversation
        flow_data1 = await conversation_flow_manager.process_conversation_turn(
            "Analyze AAPL", session_id, mock_response
        )
        
        # Second conversation - should remember context
        flow_data2 = await conversation_flow_manager.process_conversation_turn(
            "What about the options?", session_id, mock_response
        )
        
        context = flow_data2["conversation_context"]
        assert len(context.conversation_history) >= 2
        assert "AAPL" in context.active_symbols


# Utility functions for running tests
async def run_basic_tests():
    """Run basic functionality tests"""
    print("🔮 Running Predicto Integration Tests...")
    
    test_suite = TestPredictoIntegration()
    
    # Test basic initialization
    predicto_engine = PredictoConversationalEngine()
    await predicto_engine.initialize()
    
    print("✅ Predicto Engine initialized successfully")
    
    # Test basic conversation
    mock_orchestrator = type('MockOrchestrator', (), {
        'market_engine': type('MockMarketEngine', (), {
            'get_quote': lambda self, symbol: {"symbol": symbol, "price": 150.0}
        })()
    })()
    
    response = await predicto_engine.process_conversation(
        "Hello Predicto, analyze AAPL", "test", mock_orchestrator
    )
    
    print(f"✅ Basic conversation test passed: {response.response[:100]}...")
    
    await predicto_engine.cleanup()
    print("🔮 Basic tests completed successfully!")


if __name__ == "__main__":
    # Run basic tests if executed directly
    asyncio.run(run_basic_tests())
